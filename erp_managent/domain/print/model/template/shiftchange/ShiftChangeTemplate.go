package shiftchange

import (
	"fmt"
	"voderpltvv/erp_managent/domain/print/model/common"
	shiftchangeModel "voderpltvv/erp_managent/domain/print/model/shiftchange"
	"voderpltvv/erp_managent/service/po"
)

// ShiftChangeTemplate 交班单模板实体
type ShiftChangeTemplate struct {
	*common.PrintTemplateEntity                             // 基础模板实体
	Config                      *ShiftChangeTemplateConfig  // 交班单配置
	Content                     *ShiftChangeTemplateContent // 交班单模板内容
}

// NewShiftChangeTemplate 创建新的交班单模板
func NewShiftChangeTemplate(venueId, name string, copies int, printMode, paperSize string) (*ShiftChangeTemplate, error) {
	// 创建基础模板实体
	entity, err := common.NewPrintTemplateEntity(venueId, string(po.PrintTemplateTypeShiftChange), name, copies, printMode, paperSize)
	if err != nil {
		return nil, fmt.Errorf("创建基础模板实体失败: %w", err)
	}

	return &ShiftChangeTemplate{
		PrintTemplateEntity: entity,
		Config:              NewShiftChangeTemplateConfig(),
		Content:             NewShiftChangeTemplateContent(),
	}, nil
}

// NewShiftChangeTemplateFromPO 从PO创建交班单模板
func NewShiftChangeTemplateFromPO(printTemplatePO *po.PrintTemplate) (*ShiftChangeTemplate, error) {
	if printTemplatePO == nil {
		return nil, fmt.Errorf("打印模板PO不能为空")
	}

	// 验证模板类型
	if getStringValue(printTemplatePO.TemplateType) != string(po.PrintTemplateTypeShiftChange) {
		return nil, fmt.Errorf("模板类型不是交班单")
	}

	// 创建基础模板实体
	entity, err := common.NewPrintTemplateEntityFromPO(printTemplatePO)
	if err != nil {
		return nil, fmt.Errorf("创建基础模板实体失败: %w", err)
	}

	// 解析配置
	config := NewShiftChangeTemplateConfig()
	if configJson := getStringValue(printTemplatePO.Config); configJson != "" {
		if err := config.FromJSON(configJson); err != nil {
			return nil, fmt.Errorf("解析交班单配置失败: %w", err)
		}
	}

	// 解析内容
	content := NewShiftChangeTemplateContent()
	if contentJson := getStringValue(printTemplatePO.TemplateContent); contentJson != "" {
		if err := content.FromJSON(contentJson); err != nil {
			return nil, fmt.Errorf("解析交班单内容失败: %w", err)
		}
	}

	return &ShiftChangeTemplate{
		PrintTemplateEntity: entity,
		Config:              config,
		Content:             content,
	}, nil
}

// GetConfig 获取交班单配置
func (t *ShiftChangeTemplate) GetConfig() *ShiftChangeTemplateConfig {
	return t.Config
}

// SetConfig 设置交班单配置
func (t *ShiftChangeTemplate) SetConfig(config *ShiftChangeTemplateConfig) {
	t.Config = config
}

// GetContent 获取交班单内容
func (t *ShiftChangeTemplate) GetContent() *ShiftChangeTemplateContent {
	return t.Content
}

// SetContent 设置交班单内容
func (t *ShiftChangeTemplate) SetContent(content *ShiftChangeTemplateContent) {
	t.Content = content
}

// BuildContentFromData 从交班单数据构建交班单内容
func (t *ShiftChangeTemplate) BuildContentFromData(data *shiftchangeModel.ShiftChangeBillData) error {
	if t.Config == nil {
		return fmt.Errorf("交班单配置不能为空")
	}

	// 创建新的内容
	content := NewShiftChangeTemplateContent()

	// 根据数据和配置构建内容
	err := content.BuildFromShiftChangeBillData(data, t.Config, t.GetVenueId())
	if err != nil {
		return fmt.Errorf("构建交班单内容失败: %w", err)
	}

	// 设置内容
	t.Content = content
	return nil
}

// GetConfigJson 获取配置JSON
func (t *ShiftChangeTemplate) GetConfigJson() (string, error) {
	if t.Config == nil {
		return "", nil
	}
	return t.Config.ToJSON()
}

// GetContentJson 获取内容JSON
func (t *ShiftChangeTemplate) GetContentJson() (string, error) {
	if t.Content == nil {
		return "", nil
	}
	return t.Content.ToJSON()
}

// IsSplitEnabled 是否启用拆分
func (t *ShiftChangeTemplate) IsSplitEnabled() bool {
	if t.Config == nil {
		return false
	}
	return t.Config.SplitShiftHandoverForm
}

// SetSplitEnabled 设置是否启用拆分
func (t *ShiftChangeTemplate) SetSplitEnabled(enabled bool) {
	if t.Config == nil {
		t.Config = NewShiftChangeTemplateConfig()
	}
	t.Config.SplitShiftHandoverForm = enabled
}

// 辅助函数：获取字符串值
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}
