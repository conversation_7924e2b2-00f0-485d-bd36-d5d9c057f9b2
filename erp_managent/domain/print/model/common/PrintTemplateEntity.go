package common

import (
	"fmt"
	"voderpltvv/erp_managent/service/po"
)

// PrintTemplateEntity 打印模板基础实体
type PrintTemplateEntity struct {
	// 基本信息
	id           string
	venueId      string
	templateType string
	name         string

	// 公共配置
	copies    int
	printMode string
	paperSize string

	// 状态控制
	isEnabled bool
	isDefault bool
	remark    string

	// 元数据
	ctime   int64
	utime   int64
	state   int
	version int
}

// NewPrintTemplateEntity 创建新的打印模板基础实体
func NewPrintTemplateEntity(venueId, templateType, name string, copies int, printMode, paperSize string) (*PrintTemplateEntity, error) {
	if venueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	if templateType == "" {
		return nil, fmt.Errorf("模板类型不能为空")
	}

	return &PrintTemplateEntity{
		venueId:      venueId,
		templateType: templateType,
		name:         name,
		copies:       copies,
		printMode:    printMode,
		paperSize:    paperSize,
		isEnabled:    true,
		isDefault:    false,
		state:        0,
		version:      0,
	}, nil
}

// NewPrintTemplateEntityFromPO 从PO创建打印模板基础实体
func NewPrintTemplateEntityFromPO(printTemplatePO *po.PrintTemplate) (*PrintTemplateEntity, error) {
	if printTemplatePO == nil {
		return nil, fmt.Errorf("打印模板PO不能为空")
	}

	return &PrintTemplateEntity{
		id:           getStringValue(printTemplatePO.Id),
		venueId:      getStringValue(printTemplatePO.VenueId),
		templateType: getStringValue(printTemplatePO.TemplateType),
		name:         getStringValue(printTemplatePO.Name),
		copies:       getIntValue(printTemplatePO.Copies),
		printMode:    getStringValue(printTemplatePO.PrintMode),
		paperSize:    getStringValue(printTemplatePO.PaperSize),
		isEnabled:    getBoolValue(printTemplatePO.IsEnabled),
		isDefault:    getBoolValue(printTemplatePO.IsDefault),
		remark:       getStringValue(printTemplatePO.Remark),
		ctime:        getInt64Value(printTemplatePO.Ctime),
		utime:        getInt64Value(printTemplatePO.Utime),
		state:        getIntValue(printTemplatePO.State),
		version:      getIntValue(printTemplatePO.Version),
	}, nil
}

// GetId 获取ID
func (e *PrintTemplateEntity) GetId() string {
	return e.id
}

// SetId 设置ID
func (e *PrintTemplateEntity) SetId(id string) {
	e.id = id
}

// GetVenueId 获取门店ID
func (e *PrintTemplateEntity) GetVenueId() string {
	return e.venueId
}

// GetTemplateType 获取模板类型
func (e *PrintTemplateEntity) GetTemplateType() string {
	return e.templateType
}

// GetName 获取模板名称
func (e *PrintTemplateEntity) GetName() string {
	return e.name
}

// SetName 设置模板名称
func (e *PrintTemplateEntity) SetName(name string) {
	e.name = name
}

// GetCopies 获取打印份数
func (e *PrintTemplateEntity) GetCopies() int {
	return e.copies
}

// SetCopies 设置打印份数
func (e *PrintTemplateEntity) SetCopies(copies int) {
	e.copies = copies
}

// GetPrintMode 获取打印模式
func (e *PrintTemplateEntity) GetPrintMode() string {
	return e.printMode
}

// SetPrintMode 设置打印模式
func (e *PrintTemplateEntity) SetPrintMode(printMode string) {
	e.printMode = printMode
}

// GetPaperSize 获取纸张规格
func (e *PrintTemplateEntity) GetPaperSize() string {
	return e.paperSize
}

// SetPaperSize 设置纸张规格
func (e *PrintTemplateEntity) SetPaperSize(paperSize string) {
	e.paperSize = paperSize
}

// IsEnabled 是否启用
func (e *PrintTemplateEntity) IsEnabled() bool {
	return e.isEnabled
}

// SetEnabled 设置是否启用
func (e *PrintTemplateEntity) SetEnabled(enabled bool) {
	e.isEnabled = enabled
}

// IsDefault 是否为默认模板
func (e *PrintTemplateEntity) IsDefault() bool {
	return e.isDefault
}

// SetDefault 设置是否为默认模板
func (e *PrintTemplateEntity) SetDefault(isDefault bool) {
	e.isDefault = isDefault
}

// GetRemark 获取备注
func (e *PrintTemplateEntity) GetRemark() string {
	return e.remark
}

// SetRemark 设置备注
func (e *PrintTemplateEntity) SetRemark(remark string) {
	e.remark = remark
}

// GetCtime 获取创建时间
func (e *PrintTemplateEntity) GetCtime() int64 {
	return e.ctime
}

// GetUtime 获取更新时间
func (e *PrintTemplateEntity) GetUtime() int64 {
	return e.utime
}

// GetState 获取状态
func (e *PrintTemplateEntity) GetState() int {
	return e.state
}

// GetVersion 获取版本
func (e *PrintTemplateEntity) GetVersion() int {
	return e.version
}

// SetVersion 设置版本
func (e *PrintTemplateEntity) SetVersion(version int) {
	e.version = version
}

// 辅助函数：获取字符串值
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// 辅助函数：获取整数值
func getIntValue(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// 辅助函数：获取int64值
func getInt64Value(ptr *int64) int64 {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// 辅助函数：获取布尔值
func getBoolValue(ptr *bool) bool {
	if ptr == nil {
		return false
	}
	return *ptr
}
