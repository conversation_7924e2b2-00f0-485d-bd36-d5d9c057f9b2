package repository

import (
	"voderpltvv/erp_managent/domain/print/model/common"
	"voderpltvv/erp_managent/domain/print/model/template/shiftchange"

	"github.com/gin-gonic/gin"
)

// PrintTemplateRepository 打印模板仓储接口
type PrintTemplateRepository interface {
	// Save 保存打印模板基础实体
	Save(ctx *gin.Context, printTemplate *common.PrintTemplateEntity) error

	// FindByID 根据ID查找打印模板基础实体
	FindByID(ctx *gin.Context, id string) (*common.PrintTemplateEntity, error)

	// FindByVenueIDAndType 根据门店ID和模板类型查找打印模板基础实体
	FindByVenueIDAndType(ctx *gin.Context, venueID string, templateType string) (*common.PrintTemplateEntity, error)

	// FindByVenueID 根据门店ID查找所有打印模板基础实体
	FindByVenueID(ctx *gin.Context, venueID string) ([]*common.PrintTemplateEntity, error)

	// Update 更新打印模板基础实体
	Update(ctx *gin.Context, printTemplate *common.PrintTemplateEntity) error

	// Delete 删除打印模板
	Delete(ctx *gin.Context, id string) error

	// CreateShiftChangeTemplate 创建交班单模板
	CreateShiftChangeTemplate(
		ctx *gin.Context,
		venueID string,
		name string,
		config *shiftchange.ShiftChangeTemplateConfig,
	) (*shiftchange.ShiftChangeTemplate, error)

	// GetShiftChangeTemplate 获取交班单模板
	GetShiftChangeTemplate(ctx *gin.Context, venueID string) (*shiftchange.ShiftChangeTemplate, error)

	// SaveShiftChangeTemplate 保存交班单模板
	SaveShiftChangeTemplate(ctx *gin.Context, template *shiftchange.ShiftChangeTemplate) error

	// UpdateShiftChangeTemplate 更新交班单模板
	UpdateShiftChangeTemplate(ctx *gin.Context, template *shiftchange.ShiftChangeTemplate) error
}
