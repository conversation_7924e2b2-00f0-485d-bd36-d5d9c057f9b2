package initialization

import (
	"fmt"
	"sort"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// VenueInitializationManager 门店初始化管理器
// 负责管理所有初始化器，协调初始化流程，处理依赖关系
type VenueInitializationManager struct {
	Initializers    map[string]VenueDataInitializer // 已注册的初始化器
	db              *gorm.DB                        // 数据库连接
	featureVersions map[string]string               // 功能版本管理
	TaskManager     *InitializationTaskManager      // 任务管理器
}

// NewVenueInitializationManager 创建门店初始化管理器
func NewVenueInitializationManager(db *gorm.DB) *VenueInitializationManager {
	return &VenueInitializationManager{
		Initializers:    make(map[string]VenueDataInitializer),
		db:              db,
		featureVersions: make(map[string]string),
		TaskManager:     NewInitializationTaskManager(),
	}
}

// RegisterInitializer 注册初始化器
func (m *VenueInitializationManager) RegisterInitializer(initializer VenueDataInitializer) {
	featureName := initializer.GetFeatureName()
	m.Initializers[featureName] = initializer
	m.featureVersions[featureName] = initializer.GetVersion()

	// 记录注册信息
	// util.Elog(nil).Infof("注册初始化器: %s, 版本: %s", featureName, initializer.GetVersion())
}

// InitializeVenue 初始化门店
func (m *VenueInitializationManager) InitializeVenue(ctx *gin.Context, venueId string) error {
	util.Wlog(ctx).Infof("开始初始化门店: %s", venueId)

	// 按依赖顺序排序初始化器
	sortedInitializers, err := m.sortByDependencies()
	if err != nil {
		return fmt.Errorf("排序初始化器失败: %w", err)
	}

	for _, initializer := range sortedInitializers {
		if !initializer.IsRequired() {
			util.Wlog(ctx).Infof("跳过非必需功能: %s", initializer.GetFeatureName())
			continue // 跳过非必需功能
		}

		// 检查是否已初始化
		initialized, err := m.checkFeatureInitialized(ctx, venueId, initializer.GetFeatureName())
		if err != nil {
			return fmt.Errorf("检查初始化状态失败: %w", err)
		}

		if initialized {
			util.Wlog(ctx).Infof("功能已初始化，跳过: %s", initializer.GetFeatureName())
			continue // 已初始化，跳过
		}

		// 记录开始初始化
		if err := m.recordInitStart(ctx, venueId, initializer.GetFeatureName(), initializer.GetVersion()); err != nil {
			util.Wlog(ctx).Errorf("记录初始化开始失败: %v", err)
		}

		// 执行初始化
		util.Wlog(ctx).Infof("开始初始化功能: %s", initializer.GetFeatureName())
		if err := initializer.InitializeForVenue(ctx, venueId); err != nil {
			// 记录失败结果
			m.recordInitResult(ctx, venueId, initializer.GetFeatureName(), false, err.Error())
			return fmt.Errorf("初始化功能[%s]失败: %w", initializer.GetFeatureName(), err)
		}

		// 记录成功结果
		m.recordInitResult(ctx, venueId, initializer.GetFeatureName(), true, "")
		util.Wlog(ctx).Infof("功能初始化成功: %s", initializer.GetFeatureName())
	}

	util.Wlog(ctx).Infof("门店初始化完成: %s", venueId)
	return nil
}

// InitializeFeatureForVenue 为门店初始化特定功能
func (m *VenueInitializationManager) InitializeFeatureForVenue(ctx *gin.Context, venueId, featureName string) error {
	initializer, exists := m.Initializers[featureName]
	if !exists {
		return fmt.Errorf("功能[%s]的初始化器不存在", featureName)
	}

	// 检查是否已初始化
	initialized, err := m.checkFeatureInitialized(ctx, venueId, featureName)
	if err != nil {
		return fmt.Errorf("检查初始化状态失败: %w", err)
	}

	if initialized {
		return nil // 已初始化，跳过
	}

	// 记录开始初始化
	if err := m.recordInitStart(ctx, venueId, featureName, initializer.GetVersion()); err != nil {
		util.Wlog(ctx).Errorf("记录初始化开始失败: %v", err)
	}

	// 执行初始化
	if err := initializer.InitializeForVenue(ctx, venueId); err != nil {
		// 记录失败结果
		m.recordInitResult(ctx, venueId, featureName, false, err.Error())
		return err
	}

	// 记录成功结果
	m.recordInitResult(ctx, venueId, featureName, true, "")
	return nil
}

// sortByDependencies 按依赖关系排序初始化器
func (m *VenueInitializationManager) sortByDependencies() ([]VenueDataInitializer, error) {
	// 构建依赖图
	graph := make(map[string][]string)
	inDegree := make(map[string]int)

	// 初始化入度
	for featureName := range m.Initializers {
		inDegree[featureName] = 0
		graph[featureName] = []string{}
	}

	// 构建依赖关系
	for featureName, initializer := range m.Initializers {
		dependencies := initializer.GetDependencies()
		for _, dep := range dependencies {
			if _, exists := m.Initializers[dep]; !exists {
				return nil, fmt.Errorf("依赖的功能[%s]不存在", dep)
			}
			graph[dep] = append(graph[dep], featureName)
			inDegree[featureName]++
		}
	}

	// 拓扑排序
	var queue []string
	for featureName, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, featureName)
		}
	}

	var result []VenueDataInitializer
	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		result = append(result, m.Initializers[current])

		for _, neighbor := range graph[current] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	if len(result) != len(m.Initializers) {
		return nil, fmt.Errorf("检测到循环依赖")
	}

	return result, nil
}

// checkFeatureInitialized 检查功能是否已初始化
func (m *VenueInitializationManager) checkFeatureInitialized(ctx *gin.Context, venueId, featureName string) (bool, error) {
	var record po.VenueFeatureInitialization
	err := m.db.Where("venue_id = ? AND feature_name = ? AND status = ?",
		venueId, featureName, _const.INIT_STATUS_SUCCESS).First(&record).Error

	if err == gorm.ErrRecordNotFound {
		return false, nil
	}
	if err != nil {
		return false, err
	}

	return true, nil
}

// recordInitStart 记录初始化开始
func (m *VenueInitializationManager) recordInitStart(ctx *gin.Context, venueId, featureName, version string) error {
	now := time.Now().Unix()
	id := util.GetSnowflakeID()

	record := &po.VenueFeatureInitialization{
		Id:          &id,
		VenueId:     &venueId,
		FeatureName: &featureName,
		Version:     &version,
		Status:      util.GetItPtr(_const.INIT_STATUS_IN_PROGRESS),
		RetryCount:  util.GetItPtr(0),
		InitTime:    &now,
		Ctime:       &now,
		Utime:       &now,
		State:       util.GetItPtr(0),
		VersionNum:  util.GetItPtr(0),
	}

	return m.db.Create(record).Error
}

// recordInitResult 记录初始化结果
func (m *VenueInitializationManager) recordInitResult(ctx *gin.Context, venueId, featureName string, success bool, errorMessage string) {
	now := time.Now().Unix()
	status := _const.INIT_STATUS_SUCCESS
	if !success {
		status = _const.INIT_STATUS_FAILED
	}

	updates := map[string]interface{}{
		"status":        status,
		"init_time":     now,
		"utime":         now,
		"error_message": errorMessage,
	}

	err := m.db.Model(&po.VenueFeatureInitialization{}).
		Where("venue_id = ? AND feature_name = ?", venueId, featureName).
		Updates(updates).Error

	if err != nil {
		util.Wlog(ctx).Errorf("记录初始化结果失败: %v", err)
	}
}

// GetRegisteredFeatures 获取已注册的功能列表
func (m *VenueInitializationManager) GetRegisteredFeatures() []string {
	var features []string
	for featureName := range m.Initializers {
		features = append(features, featureName)
	}
	sort.Strings(features)
	return features
}
