package impl

import (
	"fmt"
	"voderpltvv/erp_managent/application/venue"
	"voderpltvv/erp_managent/domain/venue/initialization/service"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// FeatureDeploymentServiceImpl 功能部署服务实现
type FeatureDeploymentServiceImpl struct {
	initService                  *venue.VenueInitializationService
	systemFeatureVersionService *impl.SystemFeatureVersionService
}

// NewFeatureDeploymentService 创建功能部署服务
func NewFeatureDeploymentService() service.FeatureDeploymentService {
	return &FeatureDeploymentServiceImpl{
		initService:                  venue.NewVenueInitializationService(),
		systemFeatureVersionService: impl.NewSystemFeatureVersionService(),
	}
}

// DetectNewFeatures 检测新功能
func (s *FeatureDeploymentServiceImpl) DetectNewFeatures(ctx *gin.Context) ([]string, error) {
	util.Wlog(ctx).Info("开始检测新功能")

	// 获取所有已注册的初始化器
	registeredFeatures, err := s.GetRegisteredFeatures(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取已注册功能失败: %w", err)
	}

	var newFeatures []string

	// 检查每个已注册的功能是否在数据库中存在
	for featureName, version := range registeredFeatures {
		exists, err := s.CheckFeatureExists(ctx, featureName, version)
		if err != nil {
			util.Wlog(ctx).Errorf("检查功能[%s]版本[%s]是否存在失败: %v", featureName, version, err)
			continue
		}

		if !exists {
			util.Wlog(ctx).Infof("发现新功能: %s (版本: %s)", featureName, version)
			newFeatures = append(newFeatures, featureName)
		}
	}

	util.Wlog(ctx).Infof("检测完成，发现 %d 个新功能: %v", len(newFeatures), newFeatures)
	return newFeatures, nil
}

// TriggerBatchInitialization 触发批量初始化
func (s *FeatureDeploymentServiceImpl) TriggerBatchInitialization(ctx *gin.Context, featureName string) (string, error) {
	util.Wlog(ctx).Infof("为功能[%s]触发批量初始化", featureName)

	// 调用应用服务执行批量初始化
	taskId, err := s.initService.InitializeExistingVenues(ctx, featureName)
	if err != nil {
		return "", fmt.Errorf("触发批量初始化失败: %w", err)
	}

	util.Wlog(ctx).Infof("功能[%s]批量初始化任务创建成功，任务ID: %s", featureName, taskId)
	return taskId, nil
}

// AutoDetectAndInitialize 自动检测并初始化
func (s *FeatureDeploymentServiceImpl) AutoDetectAndInitialize(ctx *gin.Context) error {
	util.Wlog(ctx).Info("开始自动检测新功能并初始化")

	// 检测新功能
	newFeatures, err := s.DetectNewFeatures(ctx)
	if err != nil {
		return fmt.Errorf("检测新功能失败: %w", err)
	}

	if len(newFeatures) == 0 {
		util.Wlog(ctx).Info("没有发现新功能，无需初始化")
		return nil
	}

	// 为每个新功能触发批量初始化
	var failedFeatures []string
	for _, featureName := range newFeatures {
		taskId, err := s.TriggerBatchInitialization(ctx, featureName)
		if err != nil {
			util.Wlog(ctx).Errorf("功能[%s]自动批量初始化失败: %v", featureName, err)
			failedFeatures = append(failedFeatures, featureName)
			continue
		}
		util.Wlog(ctx).Infof("功能[%s]自动批量初始化成功，任务ID: %s", featureName, taskId)
	}

	if len(failedFeatures) > 0 {
		return fmt.Errorf("以下功能自动初始化失败: %v", failedFeatures)
	}

	util.Wlog(ctx).Infof("所有新功能自动初始化完成，共处理 %d 个功能", len(newFeatures))
	return nil
}

// GetRegisteredFeatures 获取已注册的功能列表
func (s *FeatureDeploymentServiceImpl) GetRegisteredFeatures(ctx *gin.Context) (map[string]string, error) {
	// 从初始化管理器获取已注册的初始化器
	manager := s.initService.GetManager()
	features := make(map[string]string)

	for featureName, initializer := range manager.Initializers {
		features[featureName] = initializer.GetVersion()
	}

	util.Wlog(ctx).Infof("获取到 %d 个已注册功能: %v", len(features), features)
	return features, nil
}

// CheckFeatureExists 检查功能是否存在于数据库
func (s *FeatureDeploymentServiceImpl) CheckFeatureExists(ctx *gin.Context, featureName, version string) (bool, error) {
	// 查询system_feature_versions表
	systemVersion, err := s.systemFeatureVersionService.FindByFeatureAndVersion(ctx, featureName, version)
	if err != nil {
		return false, fmt.Errorf("查询功能版本失败: %w", err)
	}

	return systemVersion != nil, nil
}
