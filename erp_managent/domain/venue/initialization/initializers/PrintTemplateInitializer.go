package initializers

import (
	"fmt"
	"time"
	"voderpltvv/erp_managent/domain/venue/initialization"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// PrintTemplateInitializer 打印模板初始化器
// 为门店创建默认打印模板和配置
type PrintTemplateInitializer struct {
	printTemplateService    *impl.PrintTemplateService
	venuePrintConfigService *impl.VenuePrintConfigService
}

// NewPrintTemplateInitializer 创建打印模板初始化器
func NewPrintTemplateInitializer() initialization.VenueDataInitializer {
	return &PrintTemplateInitializer{
		printTemplateService:    impl.NewPrintTemplateService(),
		venuePrintConfigService: impl.NewVenuePrintConfigService(),
	}
}

// GetFeatureName 获取功能名称
func (p *PrintTemplateInitializer) GetFeatureName() string {
	return "print_template"
}

// GetVersion 获取初始化版本号
func (p *PrintTemplateInitializer) GetVersion() string {
	return "1.0.0"
}

// IsRequired 是否必需初始化
func (p *PrintTemplateInitializer) IsRequired() bool {
	return true // 打印功能是必需的
}

// CheckInitialized 检查是否已初始化
func (p *PrintTemplateInitializer) CheckInitialized(ctx *gin.Context, venueId string) (bool, error) {
	// 检查是否已有门店打印配置
	configs, err := p.venuePrintConfigService.FindVenuePrintConfigsByVenue(ctx, venueId)
	if err != nil {
		return false, fmt.Errorf("查询门店打印配置失败: %w", err)
	}

	// 检查是否包含所有必需的模板类型
	requiredTypes := []string{"OPEN_TABLE", "SHIFT_CHANGE", "BILL", "REFUND"}
	existingTypes := make(map[string]bool)

	if configs != nil {
		for _, config := range *configs {
			if config.TemplateCode != nil {
				existingTypes[*config.TemplateCode] = true
			}
		}
	}

	for _, requiredType := range requiredTypes {
		if !existingTypes[requiredType] {
			util.Wlog(ctx).Infof("门店[%s]缺少打印配置: %s", venueId, requiredType)
			return false, nil
		}
	}

	util.Wlog(ctx).Infof("门店[%s]打印模板已初始化", venueId)
	return true, nil
}

// InitializeForVenue 执行初始化
func (p *PrintTemplateInitializer) InitializeForVenue(ctx *gin.Context, venueId string) error {
	util.Wlog(ctx).Infof("开始为门店[%s]初始化打印模板", venueId)

	// 需要初始化的模板类型
	templateTypes := []string{"OPEN_TABLE", "SHIFT_CHANGE", "BILL", "REFUND"}

	// 为每种模板类型创建门店配置
	var configs []*po.VenuePrintConfig

	for _, templateCode := range templateTypes {
		// 查找系统默认模板
		defaultTemplate, err := p.findOrCreateSystemDefaultTemplate(ctx, templateCode)
		if err != nil {
			return fmt.Errorf("获取默认模板[%s]失败: %w", templateCode, err)
		}

		// 创建门店打印配置
		config, err := p.createVenuePrintConfig(venueId, templateCode, defaultTemplate.GetId())
		if err != nil {
			return fmt.Errorf("创建门店打印配置[%s]失败: %w", templateCode, err)
		}

		configs = append(configs, config)
	}

	// 批量保存门店打印配置
	if err := p.venuePrintConfigService.BatchCreateVenuePrintConfigs(ctx, configs); err != nil {
		return fmt.Errorf("批量保存门店打印配置失败: %w", err)
	}

	util.Wlog(ctx).Infof("门店[%s]打印模板初始化完成，共创建%d个配置", venueId, len(configs))
	return nil
}

// GetDependencies 获取依赖关系
func (p *PrintTemplateInitializer) GetDependencies() []string {
	return []string{} // 无依赖
}

// findOrCreateSystemDefaultTemplate 查找或创建系统默认模板
func (p *PrintTemplateInitializer) findOrCreateSystemDefaultTemplate(ctx *gin.Context, templateCode string) (*po.PrintTemplate, error) {
	// 先查找是否已存在系统默认模板
	template, err := p.printTemplateService.FindSystemDefaultByTemplateCode(ctx, templateCode)
	if err == nil && template != nil {
		return template, nil
	}

	// 如果不存在，创建系统默认模板
	util.Wlog(ctx).Infof("创建系统默认模板: %s", templateCode)

	now := time.Now().Unix()
	id := util.GetSnowflakeID()

	template = &po.PrintTemplate{
		Id:              &id,
		TemplateCode:    &templateCode,
		Name:            util.GetItPtr(fmt.Sprintf("%s默认模板", templateCode)),
		LayoutContent:   util.GetItPtr(p.getDefaultLayoutContent(templateCode)),
		IsSystemDefault: util.GetItPtr(true),
		IsEnabled:       util.GetItPtr(true),
		Remark:          util.GetItPtr("系统自动创建的默认模板"),
		Ctime:           &now,
		Utime:           &now,
		State:           util.GetItPtr(0),
		Version:         util.GetItPtr(0),
	}

	if err := p.printTemplateService.CreatePrintTemplate(ctx, template); err != nil {
		return nil, fmt.Errorf("创建系统默认模板失败: %w", err)
	}

	return template, nil
}

// createVenuePrintConfig 创建门店打印配置
func (p *PrintTemplateInitializer) createVenuePrintConfig(venueId, templateCode, templateId string) (*po.VenuePrintConfig, error) {
	now := time.Now().Unix()
	id := util.GetSnowflakeID()

	config := &po.VenuePrintConfig{
		Id:                 &id,
		VenueId:            &venueId,
		TemplateCode:       &templateCode,
		SelectedTemplateId: &templateId,
		IsEnabled:          util.GetItPtr(true),
		Copies:             util.GetItPtr(1),
		BusinessConfig:     util.GetItPtr("{}"), // 默认空配置
		Remark:             util.GetItPtr("系统自动创建"),
		Ctime:              &now,
		Utime:              &now,
		State:              util.GetItPtr(0),
		Version:            util.GetItPtr(0),
	}

	return config, nil
}

// getDefaultLayoutContent 获取默认布局内容
func (p *PrintTemplateInitializer) getDefaultLayoutContent(templateCode string) string {
	switch templateCode {
	case "OPEN_TABLE":
		return `{
			"width": "80mm",
			"sections": [
				{"type": "header", "content": "开台单"},
				{"type": "venue_info", "fields": ["name", "address"]},
				{"type": "table_info", "fields": ["table_number", "open_time"]},
				{"type": "footer", "content": "谢谢惠顾"}
			]
		}`
	case "SHIFT_CHANGE":
		return `{
			"width": "80mm",
			"sections": [
				{"type": "header", "content": "交班单"},
				{"type": "venue_info", "fields": ["name"]},
				{"type": "shift_info", "fields": ["shift_time", "cashier_name"]},
				{"type": "summary", "fields": ["total_amount", "cash_amount", "card_amount"]},
				{"type": "footer", "content": "交班完成"}
			]
		}`
	case "BILL":
		return `{
			"width": "80mm",
			"sections": [
				{"type": "header", "content": "结账单"},
				{"type": "venue_info", "fields": ["name", "phone"]},
				{"type": "order_info", "fields": ["table_number", "order_time"]},
				{"type": "items", "fields": ["name", "price", "quantity", "amount"]},
				{"type": "total", "fields": ["subtotal", "discount", "total"]},
				{"type": "footer", "content": "谢谢惠顾"}
			]
		}`
	case "REFUND":
		return `{
			"width": "80mm",
			"sections": [
				{"type": "header", "content": "退款单"},
				{"type": "venue_info", "fields": ["name"]},
				{"type": "refund_info", "fields": ["original_order", "refund_time", "reason"]},
				{"type": "items", "fields": ["name", "quantity", "amount"]},
				{"type": "total", "fields": ["refund_amount"]},
				{"type": "footer", "content": "退款完成"}
			]
		}`
	default:
		return `{
			"width": "80mm",
			"sections": [
				{"type": "header", "content": "默认模板"},
				{"type": "venue_info", "fields": ["name"]},
				{"type": "footer", "content": "打印完成"}
			]
		}`
	}
}
