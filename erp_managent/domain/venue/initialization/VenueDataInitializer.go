package initialization

import "github.com/gin-gonic/gin"

// VenueDataInitializer 门店数据初始化器接口
// 所有功能初始化器的统一接口，定义了初始化的标准流程
type VenueDataInitializer interface {
	// GetFeatureName 获取功能名称
	GetFeatureName() string

	// GetVersion 获取初始化版本号
	GetVersion() string

	// IsRequired 是否必需初始化
	IsRequired() bool

	// CheckInitialized 检查是否已初始化
	CheckInitialized(ctx *gin.Context, venueId string) (bool, error)

	// InitializeForVenue 执行初始化
	InitializeForVenue(ctx *gin.Context, venueId string) error

	// GetDependencies 获取依赖关系
	GetDependencies() []string
}
