package initialization

import (
	"context"
	"fmt"
	"sync"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// InitializationTask 初始化任务（内存中的数据结构）
type InitializationTask struct {
	ID           string             // 任务ID
	FeatureName  string             // 功能名称
	Version      string             // 功能版本
	Status       string             // 任务状态
	TotalCount   int                // 总门店数
	SuccessCount int                // 成功数量
	FailedCount  int                // 失败数量
	StartTime    time.Time          // 开始时间
	EndTime      *time.Time         // 结束时间
	ErrorMessage string             // 错误信息
	CancelFunc   context.CancelFunc // 取消函数
}

// 任务状态常量
const (
	TaskStatusPending   = "PENDING"   // 待处理
	TaskStatusRunning   = "RUNNING"   // 运行中
	TaskStatusCompleted = "COMPLETED" // 已完成
	TaskStatusFailed    = "FAILED"    // 失败
	TaskStatusCancelled = "CANCELLED" // 已取消
)

// InitializationTaskManager 初始化任务管理器（内存管理）
// 管理初始化任务的生命周期，提供进度跟踪和状态监控
type InitializationTaskManager struct {
	tasks                       map[string]*InitializationTask // 任务映射
	mutex                       sync.RWMutex                   // 读写锁
	systemFeatureVersionService *impl.SystemFeatureVersionService
}

// NewInitializationTaskManager 创建任务管理器
func NewInitializationTaskManager() *InitializationTaskManager {
	return &InitializationTaskManager{
		tasks:                       make(map[string]*InitializationTask),
		systemFeatureVersionService: impl.NewSystemFeatureVersionService(),
	}
}

// CreateTask 创建初始化任务
func (m *InitializationTaskManager) CreateTask(ctx *gin.Context, featureName, version string) (string, error) {
	taskId := util.GetSnowflakeID()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 创建内存任务
	task := &InitializationTask{
		ID:           taskId,
		FeatureName:  featureName,
		Version:      version,
		Status:       TaskStatusPending,
		TotalCount:   0,
		SuccessCount: 0,
		FailedCount:  0,
		StartTime:    time.Now(),
		EndTime:      nil,
		ErrorMessage: "",
		CancelFunc:   nil,
	}

	m.tasks[taskId] = task

	// 在 system_feature_versions 表中创建或更新功能版本记录
	err := m.createOrUpdateSystemFeatureVersion(ctx, featureName, version)
	if err != nil {
		delete(m.tasks, taskId)
		return "", fmt.Errorf("创建系统功能版本记录失败: %w", err)
	}

	util.Wlog(ctx).Infof("创建初始化任务: %s, 功能: %s, 版本: %s", taskId, featureName, version)
	return taskId, nil
}

// StartTask 开始任务
func (m *InitializationTaskManager) StartTask(ctx *gin.Context, taskId string, totalCount int) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskId)
	}

	task.Status = TaskStatusRunning
	task.TotalCount = totalCount
	task.StartTime = time.Now()

	// 更新 system_feature_versions 表的状态和总门店数
	err := m.systemFeatureVersionService.UpdateInitStatus(ctx, task.FeatureName, task.Version, _const.SYSTEM_INIT_STATUS_IN_PROGRESS)
	if err != nil {
		return fmt.Errorf("更新系统功能版本状态失败: %w", err)
	}

	err = m.systemFeatureVersionService.UpdateInitProgress(ctx, task.FeatureName, task.Version, totalCount, 0, 0)
	if err != nil {
		return fmt.Errorf("更新系统功能版本进度失败: %w", err)
	}

	util.Wlog(ctx).Infof("启动初始化任务: %s, 总数: %d", taskId, totalCount)
	return nil
}

// UpdateProgress 更新任务进度
func (m *InitializationTaskManager) UpdateProgress(ctx *gin.Context, taskId string, successCount, failedCount int) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskId)
	}

	task.SuccessCount = successCount
	task.FailedCount = failedCount

	// 更新 system_feature_versions 表的进度
	err := m.systemFeatureVersionService.UpdateInitProgress(ctx, task.FeatureName, task.Version, task.TotalCount, successCount, failedCount)
	if err != nil {
		return fmt.Errorf("更新系统功能版本进度失败: %w", err)
	}

	return nil
}

// CompleteTask 完成任务
func (m *InitializationTaskManager) CompleteTask(ctx *gin.Context, taskId string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskId)
	}

	now := time.Now()
	task.Status = TaskStatusCompleted
	task.EndTime = &now

	// 更新 system_feature_versions 表的状态为完成
	err := m.systemFeatureVersionService.UpdateInitStatus(ctx, task.FeatureName, task.Version, _const.SYSTEM_INIT_STATUS_COMPLETED)
	if err != nil {
		return fmt.Errorf("更新系统功能版本状态失败: %w", err)
	}

	util.Wlog(ctx).Infof("完成初始化任务: %s", taskId)
	return nil
}

// FailTask 标记任务失败
func (m *InitializationTaskManager) FailTask(ctx *gin.Context, taskId string, errorMessage string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskId)
	}

	now := time.Now()
	task.Status = TaskStatusFailed
	task.EndTime = &now
	task.ErrorMessage = errorMessage

	util.Wlog(ctx).Errorf("任务失败: %s, 错误: %s", taskId, errorMessage)
	return nil
}

// GetTask 获取任务详情
func (m *InitializationTaskManager) GetTask(ctx *gin.Context, taskId string) (*InitializationTask, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskId)
	}

	return task, nil
}

// GetTasksByFeature 获取功能的任务列表
func (m *InitializationTaskManager) GetTasksByFeature(ctx *gin.Context, featureName string, limit int) ([]*InitializationTask, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var tasks []*InitializationTask
	for _, task := range m.tasks {
		if task.FeatureName == featureName {
			tasks = append(tasks, task)
		}
	}

	// 按开始时间倒序排列
	for i := 0; i < len(tasks)-1; i++ {
		for j := i + 1; j < len(tasks); j++ {
			if tasks[i].StartTime.Before(tasks[j].StartTime) {
				tasks[i], tasks[j] = tasks[j], tasks[i]
			}
		}
	}

	// 限制返回数量
	if limit > 0 && len(tasks) > limit {
		tasks = tasks[:limit]
	}

	return tasks, nil
}

// GetRunningTasks 获取运行中的任务
func (m *InitializationTaskManager) GetRunningTasks(ctx *gin.Context) ([]*InitializationTask, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var tasks []*InitializationTask
	for _, task := range m.tasks {
		if task.Status == TaskStatusRunning {
			tasks = append(tasks, task)
		}
	}

	return tasks, nil
}

// CancelTask 取消任务
func (m *InitializationTaskManager) CancelTask(ctx *gin.Context, taskId string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	task, exists := m.tasks[taskId]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskId)
	}

	if task.Status != TaskStatusRunning {
		return fmt.Errorf("只能取消运行中的任务")
	}

	// 调用取消函数
	if task.CancelFunc != nil {
		task.CancelFunc()
	}

	now := time.Now()
	task.Status = TaskStatusCancelled
	task.EndTime = &now

	util.Wlog(ctx).Infof("取消初始化任务: %s", taskId)
	return nil
}

// CleanupCompletedTasks 清理已完成的任务（避免内存泄漏）
func (m *InitializationTaskManager) CleanupCompletedTasks(ctx *gin.Context) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	cutoffTime := time.Now().Add(-24 * time.Hour) // 保留24小时内的任务

	for taskId, task := range m.tasks {
		if (task.Status == TaskStatusCompleted || task.Status == TaskStatusFailed || task.Status == TaskStatusCancelled) &&
			task.EndTime != nil && task.EndTime.Before(cutoffTime) {
			delete(m.tasks, taskId)
		}
	}
}

// createOrUpdateSystemFeatureVersion 创建或更新系统功能版本记录
func (m *InitializationTaskManager) createOrUpdateSystemFeatureVersion(ctx *gin.Context, featureName, version string) error {
	// 检查是否已存在
	_, err := m.systemFeatureVersionService.FindByFeatureAndVersion(ctx, featureName, version)
	if err == nil {
		// 已存在，直接返回
		return nil
	}

	// 不存在，创建新记录
	now := time.Now().Unix()
	id := util.GetSnowflakeID()

	record := &po.SystemFeatureVersion{
		Id:                &id,
		FeatureName:       &featureName,
		Version:           &version,
		DeployTime:        &now,
		InitStatus:        util.GetItPtr(_const.SYSTEM_INIT_STATUS_PENDING),
		TotalVenues:       util.GetItPtr(0),
		InitializedVenues: util.GetItPtr(0),
		FailedVenues:      util.GetItPtr(0),
		Ctime:             &now,
		Utime:             &now,
		State:             util.GetItPtr(0),
		VersionNum:        util.GetItPtr(0),
	}

	return m.systemFeatureVersionService.CreateSystemFeatureVersion(ctx, record)
}
