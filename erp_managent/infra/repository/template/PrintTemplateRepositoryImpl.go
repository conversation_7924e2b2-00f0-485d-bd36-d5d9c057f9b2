package template

import (
	"fmt"

	"voderpltvv/erp_managent/domain/print/model/common"
	"voderpltvv/erp_managent/domain/print/model/template/shiftchange"
	"voderpltvv/erp_managent/domain/print/repository"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PrintTemplateRepositoryImpl 打印模板仓储实现
type PrintTemplateRepositoryImpl struct {
	printTemplateService *impl.PrintTemplateService
}

// NewPrintTemplateRepositoryImpl 创建打印模板仓储实现
func NewPrintTemplateRepositoryImpl() repository.PrintTemplateRepository {
	return &PrintTemplateRepositoryImpl{
		printTemplateService: &impl.PrintTemplateService{},
	}
}

// Save 保存打印模板基础实体
func (r *PrintTemplateRepositoryImpl) Save(ctx *gin.Context, printTemplate *common.PrintTemplateEntity) error {
	// 转换为PO
	po := r.convertEntityToPO(printTemplate)

	// 保存
	return r.printTemplateService.CreatePrintTemplate(ctx, po)
}

// FindByID 根据ID查找打印模板基础实体
func (r *PrintTemplateRepositoryImpl) FindByID(ctx *gin.Context, id string) (*common.PrintTemplateEntity, error) {
	// 查询PO
	po, err := r.printTemplateService.FindPrintTemplateById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查找打印模板失败: %w", err)
	}

	if po == nil {
		return nil, nil
	}

	// 转换为基础实体
	return common.NewPrintTemplateEntityFromPO(po)
}

// FindByVenueIDAndType 根据门店ID和模板类型查找打印模板基础实体
func (r *PrintTemplateRepositoryImpl) FindByVenueIDAndType(ctx *gin.Context, venueID string, templateType string) (*common.PrintTemplateEntity, error) {
	// 由于现有的QueryPrintTemplateReqDto不支持VenueId和TemplateType字段，直接使用数据库查询
	po := &po.PrintTemplate{}
	err := model.DBSlave.Self.Where("venue_id = ? AND template_type = ? AND state = ?", venueID, templateType, 0).First(po).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查找打印模板失败: %w", err)
	}

	// 转换为基础实体
	return common.NewPrintTemplateEntityFromPO(po)
}

// FindByVenueID 根据门店ID查找所有打印模板基础实体
func (r *PrintTemplateRepositoryImpl) FindByVenueID(ctx *gin.Context, venueID string) ([]*common.PrintTemplateEntity, error) {
	// 由于现有的QueryPrintTemplateReqDto不支持VenueId字段，直接使用数据库查询
	var pos []*po.PrintTemplate
	err := model.DBSlave.Self.Where("venue_id = ? AND state = ?", venueID, 0).Find(&pos).Error
	if err != nil {
		return nil, fmt.Errorf("查找打印模板失败: %w", err)
	}

	// 转换为基础实体
	var templates []*common.PrintTemplateEntity
	for _, po := range pos {
		template, err := common.NewPrintTemplateEntityFromPO(po)
		if err != nil {
			continue // 跳过转换失败的记录
		}
		templates = append(templates, template)
	}

	return templates, nil
}

// Update 更新打印模板基础实体
func (r *PrintTemplateRepositoryImpl) Update(ctx *gin.Context, printTemplate *common.PrintTemplateEntity) error {
	// 转换为PO
	po := r.convertEntityToPO(printTemplate)

	// 更新
	return r.printTemplateService.UpdatePrintTemplate(ctx, po)
}

// Delete 删除打印模板
func (r *PrintTemplateRepositoryImpl) Delete(ctx *gin.Context, id string) error {
	return r.printTemplateService.DeletePrintTemplate(ctx, id)
}

// CreateShiftChangeTemplate 创建交班单模板
func (r *PrintTemplateRepositoryImpl) CreateShiftChangeTemplate(
	ctx *gin.Context,
	venueID string,
	name string,
	config *shiftchange.ShiftChangeTemplateConfig,
) (*shiftchange.ShiftChangeTemplate, error) {
	// 创建交班单模板
	template, err := shiftchange.NewShiftChangeTemplate(venueID, name, 1, "NORMAL", "80MM")
	if err != nil {
		return nil, fmt.Errorf("创建交班单模板失败: %w", err)
	}

	// 设置配置
	template.SetConfig(config)

	// 保存
	err = r.SaveShiftChangeTemplate(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("保存交班单模板失败: %w", err)
	}

	return template, nil
}

// GetShiftChangeTemplate 获取交班单模板
func (r *PrintTemplateRepositoryImpl) GetShiftChangeTemplate(ctx *gin.Context, venueID string) (*shiftchange.ShiftChangeTemplate, error) {
	// 查询交班单类型的模板
	templatePO := &po.PrintTemplate{}
	err := model.DBSlave.Self.Where("venue_id = ? AND template_type = ? AND state = ?", venueID, string(po.PrintTemplateTypeShiftChange), 0).First(templatePO).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查找交班单模板失败: %w", err)
	}

	// 转换为交班单模板
	return shiftchange.NewShiftChangeTemplateFromPO(templatePO)
}

// SaveShiftChangeTemplate 保存交班单模板
func (r *PrintTemplateRepositoryImpl) SaveShiftChangeTemplate(ctx *gin.Context, template *shiftchange.ShiftChangeTemplate) error {
	// 转换为PO
	po := r.convertShiftChangeTemplateToPO(template)

	// 保存
	return r.printTemplateService.CreatePrintTemplate(ctx, po)
}

// UpdateShiftChangeTemplate 更新交班单模板
func (r *PrintTemplateRepositoryImpl) UpdateShiftChangeTemplate(ctx *gin.Context, template *shiftchange.ShiftChangeTemplate) error {
	// 转换为PO
	po := r.convertShiftChangeTemplateToPO(template)

	// 更新
	return r.printTemplateService.UpdatePrintTemplate(ctx, po)
}

// convertEntityToPO 将基础实体转换为PO
func (r *PrintTemplateRepositoryImpl) convertEntityToPO(entity *common.PrintTemplateEntity) *po.PrintTemplate {
	return &po.PrintTemplate{
		Id:           util.GetItPtr(entity.GetId()),
		VenueId:      util.GetItPtr(entity.GetVenueId()),
		TemplateType: util.GetItPtr(entity.GetTemplateType()),
		Name:         util.GetItPtr(entity.GetName()),
		Copies:       util.GetItPtr(entity.GetCopies()),
		PrintMode:    util.GetItPtr(entity.GetPrintMode()),
		PaperSize:    util.GetItPtr(entity.GetPaperSize()),
		IsEnabled:    util.GetItPtr(entity.IsEnabled()),
		IsDefault:    util.GetItPtr(entity.IsDefault()),
		Remark:       util.GetItPtr(entity.GetRemark()),
		Ctime:        util.GetItPtr(entity.GetCtime()),
		Utime:        util.GetItPtr(entity.GetUtime()),
		State:        util.GetItPtr(entity.GetState()),
		Version:      util.GetItPtr(entity.GetVersion()),
	}
}

// convertShiftChangeTemplateToPO 将交班单模板转换为PO
func (r *PrintTemplateRepositoryImpl) convertShiftChangeTemplateToPO(template *shiftchange.ShiftChangeTemplate) *po.PrintTemplate {
	// 获取配置JSON
	configJson, _ := template.GetConfigJson()

	// 获取内容JSON
	contentJson, _ := template.GetContentJson()

	return &po.PrintTemplate{
		Id:              util.GetItPtr(template.GetId()),
		VenueId:         util.GetItPtr(template.GetVenueId()),
		TemplateType:    util.GetItPtr(template.GetTemplateType()),
		Name:            util.GetItPtr(template.GetName()),
		Copies:          util.GetItPtr(template.GetCopies()),
		PrintMode:       util.GetItPtr(template.GetPrintMode()),
		PaperSize:       util.GetItPtr(template.GetPaperSize()),
		Config:          util.GetItPtr(configJson),
		TemplateContent: util.GetItPtr(contentJson),
		IsEnabled:       util.GetItPtr(template.IsEnabled()),
		IsDefault:       util.GetItPtr(template.IsDefault()),
		Remark:          util.GetItPtr(template.GetRemark()),
		Ctime:           util.GetItPtr(template.GetCtime()),
		Utime:           util.GetItPtr(template.GetUtime()),
		State:           util.GetItPtr(template.GetState()),
		Version:         util.GetItPtr(template.GetVersion()),
	}
}
