package venue

import (
	"context"
	"fmt"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/domain/venue/initialization"
	"voderpltvv/erp_managent/domain/venue/initialization/initializers"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// VenueInitializationService 门店初始化服务
// 处理新门店创建和批量初始化的应用服务
type VenueInitializationService struct {
	manager      *initialization.VenueInitializationManager
	venueService *impl.VenueService
}

// NewVenueInitializationService 创建门店初始化服务
func NewVenueInitializationService() *VenueInitializationService {
	service := &VenueInitializationService{
		manager:      initialization.NewVenueInitializationManager(model.DBMaster.Self),
		venueService: impl.NewVenueService(),
	}

	// 注册所有初始化器
	service.registerInitializers()

	return service
}

// registerInitializers 注册所有初始化器
func (s *VenueInitializationService) registerInitializers() {
	// 注册打印模板初始化器
	s.manager.RegisterInitializer(initializers.NewPrintTemplateInitializer())

	// TODO: 注册其他初始化器
	// s.manager.RegisterInitializer(initializers.NewPricePlanInitializer())
	// s.manager.RegisterInitializer(initializers.NewInventoryInitializer())
}

// InitializeNewVenue 初始化新门店
func (s *VenueInitializationService) InitializeNewVenue(ctx *gin.Context, venueId string) error {
	util.Wlog(ctx).Infof("开始初始化新门店: %s", venueId)

	// 验证门店是否存在
	venue, err := s.venueService.FindVenueById(ctx, venueId)
	if err != nil {
		return fmt.Errorf("门店[%s]不存在: %w", venueId, err)
	}
	if venue == nil {
		return fmt.Errorf("门店[%s]不存在", venueId)
	}

	// 执行初始化
	if err := s.manager.InitializeVenue(ctx, venueId); err != nil {
		util.Wlog(ctx).Errorf("门店[%s]初始化失败: %v", venueId, err)
		return fmt.Errorf("门店初始化失败: %w", err)
	}

	util.Wlog(ctx).Infof("门店[%s]初始化完成", venueId)
	return nil
}

// InitializeExistingVenues 批量初始化已有门店
func (s *VenueInitializationService) InitializeExistingVenues(ctx *gin.Context, featureName string) (string, error) {
	util.Wlog(ctx).Infof("开始批量初始化功能: %s", featureName)

	// 获取功能版本
	initializer, exists := s.manager.Initializers[featureName]
	if !exists {
		return "", fmt.Errorf("功能[%s]未注册", featureName)
	}
	version := initializer.GetVersion()

	// 1. 创建初始化任务
	taskId, err := s.manager.TaskManager.CreateTask(ctx, featureName, version)
	if err != nil {
		return "", fmt.Errorf("创建任务失败: %w", err)
	}

	// 2. 查询所有活跃门店
	venues, err := s.getAllActiveVenues(ctx)
	if err != nil {
		s.manager.TaskManager.FailTask(ctx, taskId, err.Error())
		return "", fmt.Errorf("获取门店列表失败: %w", err)
	}

	// 3. 启动任务
	if err := s.manager.TaskManager.StartTask(ctx, taskId, len(venues)); err != nil {
		return "", fmt.Errorf("启动任务失败: %w", err)
	}

	// 4. 创建可取消的上下文
	batchCtx, cancel := context.WithCancel(context.Background())

	// 5. 将取消函数保存到任务中
	task, _ := s.manager.TaskManager.GetTask(ctx, taskId)
	if task != nil {
		task.CancelFunc = cancel
	}

	// 6. 异步批量处理
	go func() {
		s.processBatchInitialization(batchCtx, taskId, featureName, venues)
	}()

	util.Wlog(ctx).Infof("批量初始化任务已创建: %s, 门店数量: %d", taskId, len(venues))
	return taskId, nil
}

// processBatchInitialization 处理批量初始化
func (s *VenueInitializationService) processBatchInitialization(ctx context.Context, taskId, featureName string, venues []*po.Venue) {
	ginCtx := &gin.Context{} // 创建临时上下文用于日志

	batchSize := 50
	successCount := 0
	failedCount := 0

	util.Wlog(ginCtx).Infof("开始批量处理初始化任务: %s, 功能: %s, 门店数: %d", taskId, featureName, len(venues))

	defer func() {
		// 确保任务状态被正确设置
		if ctx.Err() != nil {
			// 上下文被取消
			util.Wlog(ginCtx).Infof("批量初始化被取消: 成功%d, 失败%d", successCount, failedCount)
		} else {
			// 正常完成
			if err := s.manager.TaskManager.CompleteTask(ginCtx, taskId); err != nil {
				util.Wlog(ginCtx).Errorf("完成任务失败: %v", err)
			} else {
				util.Wlog(ginCtx).Infof("批量初始化完成: 成功%d, 失败%d", successCount, failedCount)
			}
		}
	}()

	for i := 0; i < len(venues); i += batchSize {
		// 检查是否被取消
		select {
		case <-ctx.Done():
			util.Wlog(ginCtx).Infof("批量初始化被取消，已处理: 成功%d, 失败%d", successCount, failedCount)
			return
		default:
		}

		end := i + batchSize
		if end > len(venues) {
			end = len(venues)
		}

		batch := venues[i:end]
		for _, venue := range batch {
			// 再次检查是否被取消
			select {
			case <-ctx.Done():
				return
			default:
			}

			if venue.Id == nil {
				failedCount++
				continue
			}

			if err := s.manager.InitializeFeatureForVenue(ginCtx, *venue.Id, featureName); err != nil {
				failedCount++
				util.Wlog(ginCtx).Errorf("门店[%s]功能[%s]初始化失败: %v", *venue.Id, featureName, err)
			} else {
				successCount++
				util.Wlog(ginCtx).Infof("门店[%s]功能[%s]初始化成功", *venue.Id, featureName)
			}
		}

		// 更新任务进度
		if err := s.manager.TaskManager.UpdateProgress(ginCtx, taskId, successCount, failedCount); err != nil {
			util.Wlog(ginCtx).Errorf("更新任务进度失败: %v", err)
		}

		// 批次间暂停（可被取消中断）
		select {
		case <-ctx.Done():
			return
		case <-time.After(time.Second * 2):
		}
	}

	// 这里不需要调用CompleteTask，因为defer中已经处理了
}

// getAllActiveVenues 获取所有活跃门店
func (s *VenueInitializationService) getAllActiveVenues(ctx *gin.Context) ([]*po.Venue, error) {
	var venues []*po.Venue

	// 查询所有活跃门店（审核状态为通过的门店）
	err := model.DBSlave.Self.Model(&po.Venue{}).
		Where("audit_status = ?", 1). // 审核通过
		Where("state = ?", 0).        // 正常状态
		Find(&venues).Error

	if err != nil {
		return nil, fmt.Errorf("查询活跃门店失败: %w", err)
	}

	util.Wlog(ctx).Infof("查询到%d个活跃门店", len(venues))
	return venues, nil
}

// GetInitializationStatus 获取门店初始化状态
func (s *VenueInitializationService) GetInitializationStatus(ctx *gin.Context, venueId string) ([]*po.VenueFeatureInitialization, error) {
	var records []*po.VenueFeatureInitialization

	err := model.DBSlave.Self.Where("venue_id = ?", venueId).
		Order("feature_name, ctime DESC").
		Find(&records).Error

	if err != nil {
		return nil, fmt.Errorf("查询初始化状态失败: %w", err)
	}

	return records, nil
}

// GetTaskProgress 获取任务进度
func (s *VenueInitializationService) GetTaskProgress(ctx *gin.Context, taskId string) (*initialization.InitializationTask, error) {
	return s.manager.TaskManager.GetTask(ctx, taskId)
}

// GetRegisteredFeatures 获取已注册的功能列表
func (s *VenueInitializationService) GetRegisteredFeatures() []string {
	return s.manager.GetRegisteredFeatures()
}

// RetryFailedInitialization 重试失败的初始化
func (s *VenueInitializationService) RetryFailedInitialization(ctx *gin.Context, venueId, featureName string) error {
	util.Wlog(ctx).Infof("重试门店[%s]功能[%s]初始化", venueId, featureName)

	// 重置状态为待初始化
	err := model.DBMaster.Self.Model(&po.VenueFeatureInitialization{}).
		Where("venue_id = ? AND feature_name = ?", venueId, featureName).
		Updates(map[string]interface{}{
			"status":        _const.INIT_STATUS_PENDING,
			"error_message": "",
			"utime":         time.Now().Unix(),
		}).Error

	if err != nil {
		return fmt.Errorf("重置初始化状态失败: %w", err)
	}

	// 重新执行初始化
	return s.manager.InitializeFeatureForVenue(ctx, venueId, featureName)
}
