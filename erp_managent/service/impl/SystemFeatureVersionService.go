package impl

import (
	"time"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SystemFeatureVersionService struct {
}

func NewSystemFeatureVersionService() *SystemFeatureVersionService {
	return &SystemFeatureVersionService{}
}

func (service *SystemFeatureVersionService) CreateSystemFeatureVersion(logCtx *gin.Context, record *po.SystemFeatureVersion) error {
	return Save(record)
}

func (service *SystemFeatureVersionService) CreateSystemFeatureVersionWithTx(logCtx *gin.Context, record *po.SystemFeatureVersion, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

func (service *SystemFeatureVersionService) UpdateSystemFeatureVersion(logCtx *gin.Context, record *po.SystemFeatureVersion) error {
	return Update(record)
}

func (service *SystemFeatureVersionService) UpdateSystemFeatureVersionPartial(logCtx *gin.Context, record *po.SystemFeatureVersion) error {
	return UpdateNotNull(record)
}

func (service *SystemFeatureVersionService) UpdateSystemFeatureVersionPartialWithTx(logCtx *gin.Context, record *po.SystemFeatureVersion, tx *gorm.DB) error {
	return UpdateNotNullWithTx(record, tx)
}

func (service *SystemFeatureVersionService) DeleteSystemFeatureVersion(logCtx *gin.Context, id string) error {
	return Delete(po.SystemFeatureVersion{Id: &id})
}

func (service *SystemFeatureVersionService) FindSystemFeatureVersionById(logCtx *gin.Context, id string) (record *po.SystemFeatureVersion, err error) {
	record = &po.SystemFeatureVersion{}
	err = model.DBMaster.Self.Where("id=?", id).First(record).Error
	return
}

func (service *SystemFeatureVersionService) FindByFeatureAndVersion(logCtx *gin.Context, featureName, version string) (record *po.SystemFeatureVersion, err error) {
	record = &po.SystemFeatureVersion{}
	err = model.DBSlave.Self.Where("feature_name=? AND version=?", featureName, version).First(record).Error
	return
}

func (service *SystemFeatureVersionService) FindByFeatureName(logCtx *gin.Context, featureName string) (list *[]po.SystemFeatureVersion, err error) {
	db := model.DBSlave.Self.Model(&po.SystemFeatureVersion{})
	if featureName != "" {
		db = db.Where("feature_name=?", featureName)
	}
	db = db.Order("deploy_time DESC")

	list = &[]po.SystemFeatureVersion{}
	result := db.Find(list)
	err = result.Error
	return
}

func (service *SystemFeatureVersionService) FindByInitStatus(logCtx *gin.Context, initStatus int) (list *[]po.SystemFeatureVersion, err error) {
	db := model.DBSlave.Self.Model(&po.SystemFeatureVersion{})
	db = db.Where("init_status=?", initStatus)
	db = db.Order("deploy_time ASC")

	list = &[]po.SystemFeatureVersion{}
	result := db.Find(list)
	err = result.Error
	return
}

func (service *SystemFeatureVersionService) UpdateInitProgress(logCtx *gin.Context, featureName, version string, totalVenues, initializedVenues, failedVenues int) error {
	updates := map[string]interface{}{
		"total_venues":       totalVenues,
		"initialized_venues": initializedVenues,
		"failed_venues":      failedVenues,
		"utime":              time.Now().Unix(),
	}

	return model.DBMaster.Self.Model(&po.SystemFeatureVersion{}).
		Where("feature_name = ? AND version = ?", featureName, version).
		Updates(updates).Error
}

func (service *SystemFeatureVersionService) UpdateInitStatus(logCtx *gin.Context, featureName, version string, initStatus int) error {
	updates := map[string]interface{}{
		"init_status": initStatus,
		"utime":       time.Now().Unix(),
	}

	return model.DBMaster.Self.Model(&po.SystemFeatureVersion{}).
		Where("feature_name = ? AND version = ?", featureName, version).
		Updates(updates).Error
}
