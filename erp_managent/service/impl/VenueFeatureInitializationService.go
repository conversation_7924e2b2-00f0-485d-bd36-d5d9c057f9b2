package impl

import (
	"time"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type VenueFeatureInitializationService struct {
}

func NewVenueFeatureInitializationService() *VenueFeatureInitializationService {
	return &VenueFeatureInitializationService{}
}

func (service *VenueFeatureInitializationService) CreateVenueFeatureInitialization(logCtx *gin.Context, record *po.VenueFeatureInitialization) error {
	return Save(record)
}

func (service *VenueFeatureInitializationService) CreateVenueFeatureInitializationWithTx(logCtx *gin.Context, record *po.VenueFeatureInitialization, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

func (service *VenueFeatureInitializationService) UpdateVenueFeatureInitialization(logCtx *gin.Context, record *po.VenueFeatureInitialization) error {
	return Update(record)
}

func (service *VenueFeatureInitializationService) UpdateVenueFeatureInitializationPartial(logCtx *gin.Context, record *po.VenueFeatureInitialization) error {
	return UpdateNotNull(record)
}

func (service *VenueFeatureInitializationService) UpdateVenueFeatureInitializationPartialWithTx(logCtx *gin.Context, record *po.VenueFeatureInitialization, tx *gorm.DB) error {
	return UpdateNotNullWithTx(record, tx)
}

func (service *VenueFeatureInitializationService) DeleteVenueFeatureInitialization(logCtx *gin.Context, id string) error {
	return Delete(po.VenueFeatureInitialization{Id: &id})
}

func (service *VenueFeatureInitializationService) FindVenueFeatureInitializationById(logCtx *gin.Context, id string) (record *po.VenueFeatureInitialization, err error) {
	record = &po.VenueFeatureInitialization{}
	err = model.DBMaster.Self.Where("id=?", id).First(record).Error
	return
}

func (service *VenueFeatureInitializationService) FindByVenueAndFeature(logCtx *gin.Context, venueId, featureName string) (record *po.VenueFeatureInitialization, err error) {
	record = &po.VenueFeatureInitialization{}
	err = model.DBSlave.Self.Where("venue_id=? AND feature_name=?", venueId, featureName).First(record).Error
	return
}

func (service *VenueFeatureInitializationService) FindByVenueId(logCtx *gin.Context, venueId string) (list *[]po.VenueFeatureInitialization, err error) {
	db := model.DBSlave.Self.Model(&po.VenueFeatureInitialization{})
	if venueId != "" {
		db = db.Where("venue_id=?", venueId)
	}
	db = db.Order("feature_name, ctime DESC")

	list = &[]po.VenueFeatureInitialization{}
	result := db.Find(list)
	err = result.Error
	return
}

func (service *VenueFeatureInitializationService) FindByStatus(logCtx *gin.Context, status int) (list *[]po.VenueFeatureInitialization, err error) {
	db := model.DBSlave.Self.Model(&po.VenueFeatureInitialization{})
	db = db.Where("status=?", status)
	db = db.Order("ctime ASC")

	list = &[]po.VenueFeatureInitialization{}
	result := db.Find(list)
	err = result.Error
	return
}

func (service *VenueFeatureInitializationService) UpdateStatus(logCtx *gin.Context, venueId, featureName string, status int, errorMessage string) error {
	updates := map[string]interface{}{
		"status":        status,
		"error_message": errorMessage,
		"utime":         time.Now().Unix(),
	}

	return model.DBMaster.Self.Model(&po.VenueFeatureInitialization{}).
		Where("venue_id = ? AND feature_name = ?", venueId, featureName).
		Updates(updates).Error
}
