package po

// VenueFeatureInitialization 门店功能初始化记录实体
// 职责：记录每个门店每个功能的初始化状态和结果
type VenueFeatureInitialization struct {
	Id           *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                // ID
	VenueId      *string `gorm:"column:venue_id;type:varchar(64);not null;index" json:"venueId"`          // 门店ID
	FeatureName  *string `gorm:"column:feature_name;type:varchar(100);not null;index" json:"featureName"` // 功能名称
	Version      *string `gorm:"column:version;type:varchar(20);not null" json:"version"`                 // 初始化版本
	Status       *int    `gorm:"column:status;type:tinyint;not null;default:0;index" json:"status"`       // 状态：0=待初始化，1=初始化中，2=成功，3=失败
	ErrorMessage *string `gorm:"column:error_message;type:text" json:"errorMessage"`                      // 错误信息
	RetryCount   *int    `gorm:"column:retry_count;type:int;not null;default:0" json:"retryCount"`        // 重试次数
	InitTime     *int64  `gorm:"column:init_time;type:bigint;not null;default:0" json:"initTime"`         // 初始化时间

	Ctime      *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`         // 创建时间
	Utime      *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`         // 更新时间
	State      *int   `gorm:"column:state;type:int;default:0" json:"state"`            // 状态
	VersionNum *int   `gorm:"column:version_num;type:int;default:0" json:"versionNum"` // 版本
}

// TableName 设置表名
func (VenueFeatureInitialization) TableName() string {
	return "venue_feature_initialization"
}

// GetId 获取ID
func (v VenueFeatureInitialization) GetId() string {
	if v.Id == nil {
		return ""
	}
	return *v.Id
}
