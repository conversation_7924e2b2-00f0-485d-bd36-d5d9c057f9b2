package application

import (
	"errors"
	. "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	venueapp "voderpltvv/erp_managent/application/venue"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

var (
	venueService          = impl.NewVenueService()
	venueAuthService      = impl.NewVenueAuthService()
	venueTransfer         = transfer.VenueTransfer{}
	employeeTransfer      = transfer.EmployeeTransfer{}
	systemRecordService   = impl.NewSystemOperationRecordService()
	cashierMachineService = impl.CashierMachineService{}
	erpUserService        = impl.ERPUserService{}
)

// VenueAppService 门店应用服务接口
type VenueAppService interface {
	// CreateVenueForMiniApp 小程序创建门店（包含员工创建和授权）
	CreateVenueForMiniApp(ctx *gin.Context, reqDto req.AddVenueForMiniAppReqDto) (*vo.NewVenueVO, error)
	// UpdateVenueAppInfo 更新门店App信息
	UpdateVenueAppInfo(ctx *gin.Context, venueId string, dogname string, pwd string) error
	// GetMyVenues 查询我的门店列表
	GetMyVenues(ctx *gin.Context, reqDto req.QueryMyVenueReqDto) ([]vo.VenueVO, error)
	// SwitchVenue 切换门店
	SwitchVenue(ctx *gin.Context, reqDto *req.SwitchVenueReqDto) (*vo.LoginVenueAndEmployeeVO, error)
}

// VenueAppServiceImpl 门店应用服务实现
type VenueAppServiceImpl struct {
}

// NewVenueAppService 创建门店应用服务
func NewVenueAppService() VenueAppService {
	return &VenueAppServiceImpl{}
}

// CreateVenueForMiniApp 小程序创建门店（包含员工创建和授权）
func (s *VenueAppServiceImpl) CreateVenueForMiniApp(ctx *gin.Context, reqDto req.AddVenueForMiniAppReqDto) (*vo.NewVenueVO, error) {
	// 1. 获取ERP用户ID
	userId := ctx.GetString("userId")
	util.Wlog(ctx).Infof("userId: %s", userId)

	// 1.1 通过userId获取用户信息
	erpUser, err := erpUserService.FindERPUserById(ctx, userId)
	if err != nil {
		return nil, err
	}

	// 2. 开启事务
	tx := model.DBMaster.Self.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	// 使用 defer 处理事务提交或回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // re-throw panic after Rollback
		}
	}()

	// 3. 创建门店
	venue := po.Venue{}
	if reqDto.Name != nil {
		venue.Name = reqDto.Name
	}
	if reqDto.Logo != nil {
		venue.Logo = reqDto.Logo
	}
	if reqDto.Address != nil {
		venue.Address = reqDto.Address
	}
	if reqDto.StartHours != nil || *reqDto.StartHours == "" {
		venue.StartHours = reqDto.StartHours
	} else {
		// 早上8点
		venue.StartHours = util.GetItPtr("10:00")
	}
	if reqDto.EndHours != nil || *reqDto.EndHours == "" {
		venue.EndHours = reqDto.EndHours
	} else {
		// 早上8点
		venue.EndHours = util.GetItPtr("02:00")
	}
	if reqDto.Photos != nil {
		venue.Photos = reqDto.Photos
	}
	if reqDto.Unionid != nil {
		venue.Unionid = reqDto.Unionid
	}
	if reqDto.ContactName != nil {
		venue.Contact = reqDto.ContactName
	}
	if reqDto.ContactPhone != nil {
		venue.ContactPhone = reqDto.ContactPhone
	}
	if reqDto.VenueType != nil {
		venueType := po.VenueType(*reqDto.VenueType)
		venue.VenueType = &venueType
	}
	if reqDto.Province != nil {
		venue.Province = reqDto.Province
	}
	if reqDto.City != nil {
		venue.City = reqDto.City
	}
	if reqDto.District != nil {
		venue.District = reqDto.District
	}
	if reqDto.Description != nil {
		venue.Description = reqDto.Description
	}
	auditStatus := 1
	venue.AuditStatus = &auditStatus

	err = venueService.CreateVenueWithTx(ctx, &venue, tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 4. 生成试用授权码（30天有效期）
	systemUserId := "system" // 系统创建的试用授权码
	validPeriod := 30        // 30天试用期
	remark := "门店创建时自动生成的试用授权码"
	// 新增合同参数（试用授权默认值）
	contractNo := ""
	contractName := ""

	_, err = venueAuthService.GenerateAuthCode(
		ctx,
		*venue.Id,
		validPeriod,
		systemUserId, // userId参数
		remark,
		contractNo,   // 新增合同编号参数
		contractName, // 新增合同名称参数
		systemUserId, // creatorId参数，使用"system"
		tx,           // 事务参数
	)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 修改试用授权码记录调用
	recordParams := impl.RecordParams{
		NewValue: map[string]interface{}{
			"valid_period": validPeriod,
			"creator":      "system",
		},
		ExtraInfo: map[string]string{"remark": remark},
	}

	if err := systemRecordService.RecordOperation(
		ctx,
		"system",
		CLIENT_MINI_PROGRAM,          // 小程序端标识
		OPERATION_TYPE_GENERATE_AUTH, // 生成授权操作
		TARGET_TYPE_AUTH_CODE,        // 授权目标类型
		*venue.Id,
		recordParams,
		tx,
	); err != nil {
		util.Wlog(ctx).Errorf("试用授权码生成记录失败: %v", err)
	}

	// 创建门店不允许绑定收银机
	// if reqDto.SceneStr != nil && *reqDto.SceneStr != "" {
	// 	// 根据 sceneStr 查询收银机记录
	// 	cashierMachine, err := cashierMachineService.FindCashierMachineBySceneStr(ctx, *reqDto.SceneStr)
	// 	if err != nil {
	// 		tx.Rollback()
	// 		return nil, err
	// 	}

	// 	// 如果找到记录，更新其 venue_id
	// 	if cashierMachine != nil {
	// 		// 更新收银机授权信息
	// 		cashierMachine.VenueId = venue.Id
	// 		// 使用事务进行更新
	// 		err = impl.UpdateNotNullWithTx(cashierMachine, tx)
	// 		if err != nil {
	// 			tx.Rollback()
	// 			return nil, err
	// 		}
	// 	}
	// 	// 如果没有找到记录，不做任何处理
	// }

	// 7. 创建员工
	employee := po.Employee{
		Phone:        erpUser.Phone, // 使用查询到的用户手机号
		Unionid:      reqDto.Unionid,
		Name:         erpUser.Name, // 使用查询到的用户名
		VenueId:      venue.Id,
		IsBoss:       util.GetItPtr(true), // 创建门店时，第一个员工自动设置为老板
		ReviewStatus: util.GetItPtr(1),    // 创建门店时，员工审核状态为通过
	}
	err = employeeService.CreateEmployeeWithTx(ctx, &employee, tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 8. 创建员工与userId的关系
	erpUserAndEmployee := &po.ERPUserAndEmployee{
		ERPUserId:  reqDto.UserId,
		EmployeeId: employee.Id,
		VenueId:    employee.VenueId,
	}
	err = erpUserAndEmployeeService.CreateERPUserAndEmployeeWithTx(ctx, erpUserAndEmployee, tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 9. 提交事务
	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 10. 门店数据初始化（在事务提交后执行，确保门店创建成功不受影响）
	venueInitService := venueapp.NewVenueInitializationService()
	if err := venueInitService.InitializeNewVenue(ctx, *venue.Id); err != nil {
		// 初始化失败不影响门店创建，只记录错误日志
		util.Wlog(ctx).Errorf("门店[%s]数据初始化失败: %v", *venue.Id, err)
	}

	// 11. 权限数据初始化（在事务提交后执行，确保门店创建成功不受影响）
	if viper.GetBool("auth.enable_permission_check") {
		// 简化权限初始化逻辑
		initPermissionForNewVenue(ctx, *employee.Id, *venue.Id)
	}

	// 12. 返回结果
	return &vo.NewVenueVO{
		EmployeeVO: employeeTransfer.PoToVo(employee),
		VenueVO:    venueTransfer.PoToVo(venue),
	}, nil
}

// UpdateVenueAppInfo 更新门店App信息
func (s *VenueAppServiceImpl) UpdateVenueAppInfo(ctx *gin.Context, venueId string, dogname string, pwd string) error {
	return venueService.UpdateVenueAppInfo(ctx, venueId, dogname, pwd)
}

// GetMyVenues 查询我的门店列表
func (s *VenueAppServiceImpl) GetMyVenues(ctx *gin.Context, reqDto req.QueryMyVenueReqDto) ([]vo.VenueVO, error) {
	// 1. 根据手机号查询ERP用户
	erpUsers, err := erpUserService.FindAllERPUser(ctx, &req.QueryERPUserReqDto{
		Phone: reqDto.Phone,
	})
	if err != nil {
		return nil, err
	}
	if len(*erpUsers) == 0 {
		return []vo.VenueVO{}, nil
	}
	userId := *(*erpUsers)[0].Id

	// 2. 根据userId查询员工关联的门店
	userEmployees, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
		ERPUserId: &userId,
	})
	if err != nil {
		return nil, err
	}
	if len(*userEmployees) == 0 {
		return []vo.VenueVO{}, nil
	}

	// 3. 获取所有venueId
	venueIds := make([]string, 0)
	for _, ue := range *userEmployees {
		if ue.VenueId != nil {
			venueIds = append(venueIds, *ue.VenueId)
		}
	}

	// 4. 查询门店信息
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Ids: &venueIds,
	})
	if err != nil {
		return nil, err
	}

	// 5. 转换为VO返回
	venueVOs := make([]vo.VenueVO, 0)
	for _, v := range *venues {
		venueVOs = append(venueVOs, venueTransfer.PoToVo(v))
	}

	return venueVOs, nil
}

// SwitchVenue 切换门店
func (s *VenueAppServiceImpl) SwitchVenue(ctx *gin.Context, reqDto *req.SwitchVenueReqDto) (*vo.LoginVenueAndEmployeeVO, error) {
	// 1. 获取ERP用户ID
	userId := ctx.GetString("userId")

	// 2. 根据userId和venueId查询员工信息
	userEmployees, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
		ERPUserId: &userId,
		VenueId:   reqDto.VenueId,
	})
	if err != nil {
		return nil, err
	}
	if len(*userEmployees) == 0 {
		return nil, errors.New("未找到该用户在该门店的员工信息")
	}
	employeeId := (*userEmployees)[0].EmployeeId

	// 3. 查询员工信息
	employee, err := employeeService.FindEmployeeById(ctx, *employeeId)
	if err != nil {
		return nil, err
	}

	// 4. 查询门店信息
	venue, err := venueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	// 5. 组装返回结果
	return &vo.LoginVenueAndEmployeeVO{
		VenueVO:         venueTransfer.PoToVo(*venue),
		LoginEmployeeVO: employeeTransfer.PoToVo(*employee),
	}, nil
}

// initPermissionForNewVenue 为新门店初始化权限，使用简化的错误处理
func initPermissionForNewVenue(ctx *gin.Context, employeeId, venueId string) {
	// 初始化基础权限数据
	if err := impl.SeedDefaultPermissionData(); err != nil {
		util.Wlog(ctx).Errorf("权限数据初始化失败: %v", err)
		return
	}

	// 为员工分配管理员角色
	permissionInitService := impl.NewPermissionDataInitService()
	if permissionInitService == nil {
		util.Wlog(ctx).Errorf("权限初始化服务创建失败")
		return
	}

	if _, err := permissionInitService.AssignSpecificEmployeeRole(ctx, employeeId, ROLE_ADMIN, venueId); err != nil {
		util.Wlog(ctx).Errorf("为员工分配管理员角色失败: %v", err)
	}
}
