-- 门店数据初始化框架相关表结构
-- 创建时间: $(date)

-- ============================================================================
-- 表一：venue_feature_initialization（门店功能初始化记录表）
-- 职责：记录每个门店每个功能的初始化状态和结果
-- ============================================================================
CREATE TABLE `venue_feature_initialization` (
    `id` varchar(64) NOT NULL COMMENT 'ID',
    `venue_id` varchar(64) NOT NULL COMMENT '门店ID',
    `feature_name` varchar(100) NOT NULL COMMENT '功能名称',
    `version` varchar(20) NOT NULL COMMENT '初始化版本',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0=待初始化，1=初始化中，2=成功，3=失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `init_time` bigint NOT NULL DEFAULT 0 COMMENT '初始化时间',
    `ctime` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime` bigint NOT NULL DEFAULT 0 COMMENT '更新时间',
    `state` int NOT NULL DEFAULT 0 COMMENT '状态',
    `version_num` int NOT NULL DEFAULT 0 COMMENT '版本',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_venue_feature` (`venue_id`, `feature_name`),
    KEY `idx_venue_id` (`venue_id`),
    KEY `idx_feature_name` (`feature_name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店功能初始化记录表';

-- ============================================================================
-- 表二：system_feature_versions（系统功能版本管理表）
-- 职责：管理系统功能版本和整体初始化进度
-- ============================================================================
CREATE TABLE `system_feature_versions` (
    `id` varchar(64) NOT NULL COMMENT 'ID',
    `feature_name` varchar(100) NOT NULL COMMENT '功能名称',
    `version` varchar(20) NOT NULL COMMENT '版本号',
    `deploy_time` bigint NOT NULL COMMENT '部署时间',
    `init_status` tinyint NOT NULL DEFAULT 0 COMMENT '初始化状态：0=待初始化，1=初始化中，2=完成',
    `total_venues` int NOT NULL DEFAULT 0 COMMENT '总门店数',
    `initialized_venues` int NOT NULL DEFAULT 0 COMMENT '已初始化门店数',
    `failed_venues` int NOT NULL DEFAULT 0 COMMENT '失败门店数',
    `ctime` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime` bigint NOT NULL DEFAULT 0 COMMENT '更新时间',
    `state` int NOT NULL DEFAULT 0 COMMENT '状态',
    `version_num` int NOT NULL DEFAULT 0 COMMENT '版本',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_feature_version` (`feature_name`, `version`),
    KEY `idx_feature_name` (`feature_name`),
    KEY `idx_deploy_time` (`deploy_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统功能版本管理表';

-- ============================================================================
-- 注意：根据架构设计文档
-- ============================================================================
-- InitializationTask 是内存中的数据结构，用于临时管理任务状态和进度监控，不需要创建对应的数据库表。
-- 持久化存储通过 venue_feature_initialization 和 system_feature_versions 两个表来实现。
-- 
-- 任务进度跟踪通过以下方式实现：
-- 1. 整体进度：通过 system_feature_versions 表的统计字段（total_venues, initialized_venues, failed_venues）
-- 2. 具体门店状态：通过 venue_feature_initialization 表记录每个门店的初始化状态
-- 3. 内存任务管理：InitializationTaskManager 在内存中管理当前运行的任务状态