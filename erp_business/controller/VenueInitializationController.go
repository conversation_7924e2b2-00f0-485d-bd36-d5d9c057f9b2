package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/venue"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// VenueInitializationController 商务后台门店初始化控制器
type VenueInitializationController struct {
	initService                       *venue.VenueInitializationService
	venueFeatureInitializationService *impl.VenueFeatureInitializationService
	systemFeatureVersionService       *impl.SystemFeatureVersionService
	venueService                      *impl.VenueService
}

// NewVenueInitializationController 创建门店初始化控制器
func NewVenueInitializationController() *VenueInitializationController {
	return &VenueInitializationController{
		initService:                       venue.NewVenueInitializationService(),
		venueFeatureInitializationService: impl.NewVenueFeatureInitializationService(),
		systemFeatureVersionService:       impl.NewSystemFeatureVersionService(),
		venueService:                      impl.NewVenueService(),
	}
}

// BatchInitFeature 批量初始化功能
// @Summary 批量初始化功能
// @Description 为所有门店批量初始化指定功能（商务后台）
// @Tags 商务后台-门店初始化
// @Accept json
// @Produce json
// @Param body body req.BatchInitFeatureReqDto true "请求体"
// @Success 200 {object} Result[vo.BatchInitFeatureRespDto] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 401 {object} Result[any] "权限不足"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/venue/batch-init-feature [post]
func (c *VenueInitializationController) BatchInitFeature(ctx *gin.Context) {
	var reqDto req.BatchInitFeatureReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误: "+err.Error())
		return
	}

	// 从商务后台上下文获取操作员信息
	staffId := ctx.GetString("staffId")
	if staffId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "未获取到操作员信息")
		return
	}

	util.Wlog(ctx).Infof("商务后台操作员[%s]开始批量初始化功能[%s]", staffId, reqDto.FeatureName)

	// 调用应用服务执行批量初始化
	taskId, err := c.initService.InitializeExistingVenues(ctx, reqDto.FeatureName)
	if err != nil {
		util.Wlog(ctx).Errorf("批量初始化功能[%s]失败: %v", reqDto.FeatureName, err)
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "批量初始化失败: "+err.Error())
		return
	}

	util.Wlog(ctx).Infof("批量初始化功能[%s]任务创建成功，任务ID: %s", reqDto.FeatureName, taskId)

	// 返回任务ID
	respDto := vo.BatchInitFeatureRespDto{
		TaskId: taskId,
	}
	Result_success[any](ctx, respDto)
}

// QueryInitStatus 查询初始化状态
// @Summary 查询门店初始化状态
// @Description 查询门店功能初始化状态列表（商务后台）
// @Tags 商务后台-门店初始化
// @Accept json
// @Produce json
// @Param body body req.QueryInitStatusReqDto true "请求体"
// @Success 200 {object} Result[vo.QueryInitStatusRespDto] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/venue/init-status [post]
func (c *VenueInitializationController) QueryInitStatus(ctx *gin.Context) {
	var reqDto req.QueryInitStatusReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误: "+err.Error())
		return
	}

	// 设置默认分页参数
	if reqDto.PageNum == nil {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil {
		reqDto.PageSize = util.GetItPtr(20)
	}

	// 查询初始化记录
	records, totalCount, err := c.venueFeatureInitializationService.FindWithPagination(ctx, reqDto.VenueId, reqDto.FeatureName, reqDto.Status, *reqDto.PageNum, *reqDto.PageSize)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询失败: "+err.Error())
		return
	}

	// 转换为VO
	var voList []vo.VenueFeatureInitializationVO

	for _, record := range *records {
		// 状态转换：int -> string
		statusStr := "unknown"
		if record.Status != nil {
			switch *record.Status {
			case 0:
				statusStr = "pending"
			case 1:
				statusStr = "running"
			case 2:
				statusStr = "completed"
			case 3:
				statusStr = "failed"
			}
		}

		voItem := vo.VenueFeatureInitializationVO{
			Id:          *record.Id,
			VenueId:     *record.VenueId,
			FeatureName: *record.FeatureName,
			Version:     *record.Version,
			Status:      statusStr,
			StartTime:   *record.InitTime, // 使用InitTime作为StartTime
			Ctime:       *record.Ctime,
			Utime:       *record.Utime,
		}

		// 设置可选字段
		if record.ErrorMessage != nil {
			voItem.ErrorMsg = *record.ErrorMessage
		}

		// 查询门店名称
		if venue, err := c.venueService.FindVenueById(ctx, *record.VenueId); err == nil && venue != nil {
			if venue.Name != nil {
				voItem.VenueName = *venue.Name
			}
		}

		voList = append(voList, voItem)
	}

	// 构造响应
	respDto := vo.QueryInitStatusRespDto{
		Records: voList,
		Total:   totalCount,
	}

	Result_success[any](ctx, respDto)
}

// QueryInitProgress 查询初始化进度
// @Summary 查询初始化进度
// @Description 查询系统级别的初始化任务进度（商务后台）
// @Tags 商务后台-门店初始化
// @Accept json
// @Produce json
// @Param body body req.QueryInitProgressReqDto true "请求体"
// @Success 200 {object} Result[vo.QueryInitProgressRespDto] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/system/init-progress [post]
func (c *VenueInitializationController) QueryInitProgress(ctx *gin.Context) {
	var reqDto req.QueryInitProgressReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误: "+err.Error())
		return
	}

	// 设置默认分页参数
	if reqDto.PageNum == nil {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil {
		reqDto.PageSize = util.GetItPtr(20)
	}

	var voList []vo.InitializationTaskVO
	var totalCount int64

	// 如果指定了TaskId，优先查询内存中的任务
	if reqDto.TaskId != nil && *reqDto.TaskId != "" {
		task, err := c.initService.GetManager().TaskManager.GetTask(ctx, *reqDto.TaskId)
		if err == nil && task != nil {
			voItem := vo.InitializationTaskVO{
				TaskId:       task.ID, // 使用ID字段
				FeatureName:  task.FeatureName,
				Version:      task.Version,
				Status:       task.Status,
				TotalVenues:  task.TotalCount, // 使用TotalCount字段
				SuccessCount: task.SuccessCount,
				FailedCount:  task.FailedCount,
				PendingCount: task.TotalCount - task.SuccessCount - task.FailedCount,
				StartTime:    task.StartTime.Unix(),
				CreatedAt:    task.StartTime.Unix(), // 使用StartTime作为CreatedAt
				UpdatedAt:    task.StartTime.Unix(), // 使用StartTime作为UpdatedAt
			}

			// 计算进度
			if task.TotalCount > 0 {
				voItem.Progress = (task.SuccessCount + task.FailedCount) * 100 / task.TotalCount
			}

			// 设置可选字段
			if task.EndTime != nil {
				voItem.EndTime = task.EndTime.Unix()
				voItem.Duration = task.EndTime.Sub(task.StartTime).Milliseconds()
			}
			if task.ErrorMessage != "" {
				voItem.ErrorMsg = task.ErrorMessage
			}

			voList = append(voList, voItem)
			totalCount = 1
		}
	} else {
		// 查询数据库中的系统功能版本记录
		systemVersions, count, err := c.systemFeatureVersionService.FindWithPagination(ctx, reqDto.FeatureName, reqDto.Status, *reqDto.PageNum, *reqDto.PageSize)
		if err != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询失败: "+err.Error())
			return
		}

		totalCount = count

		for _, version := range *systemVersions {
			// 状态转换：int -> string
			statusStr := "unknown"
			if version.InitStatus != nil {
				switch *version.InitStatus {
				case 0:
					statusStr = "pending"
				case 1:
					statusStr = "running"
				case 2:
					statusStr = "completed"
				case 3:
					statusStr = "failed"
				}
			}

			voItem := vo.InitializationTaskVO{
				TaskId:       "", // 数据库记录没有TaskId
				FeatureName:  *version.FeatureName,
				Version:      *version.Version,
				Status:       statusStr,
				TotalVenues:  *version.TotalVenues,
				SuccessCount: *version.InitializedVenues, // 使用InitializedVenues作为成功数
				FailedCount:  *version.FailedVenues,
				PendingCount: *version.TotalVenues - *version.InitializedVenues - *version.FailedVenues,
				CreatedAt:    *version.Ctime,
				UpdatedAt:    *version.Utime,
			}

			// 计算进度
			if *version.TotalVenues > 0 {
				voItem.Progress = (*version.InitializedVenues + *version.FailedVenues) * 100 / *version.TotalVenues
			}

			// 设置可选字段
			if version.DeployTime != nil {
				voItem.StartTime = *version.DeployTime
			}

			voList = append(voList, voItem)
		}
	}

	// 构造响应
	respDto := vo.QueryInitProgressRespDto{
		Tasks: voList,
		Total: totalCount,
	}

	Result_success[any](ctx, respDto)
}

// RetryFailedInit 重试失败的初始化
// @Summary 重试失败的初始化
// @Description 重试指定门店的失败初始化任务（商务后台）
// @Tags 商务后台-门店初始化
// @Accept json
// @Produce json
// @Param body body req.RetryFailedInitReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/system/retry-failed-init [post]
func (c *VenueInitializationController) RetryFailedInit(ctx *gin.Context) {
	var reqDto req.RetryFailedInitReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误: "+err.Error())
		return
	}

	// 从商务后台上下文获取操作员信息
	staffId := ctx.GetString("staffId")
	if staffId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "未获取到操作员信息")
		return
	}

	util.Wlog(ctx).Infof("商务后台操作员[%s]重试门店[%s]功能[%s]初始化", staffId, reqDto.VenueId, reqDto.FeatureName)

	// 调用初始化管理器重试单个门店的初始化
	err := c.initService.GetManager().InitializeFeatureForVenue(ctx, reqDto.VenueId, reqDto.FeatureName)
	if err != nil {
		util.Wlog(ctx).Errorf("重试门店[%s]功能[%s]初始化失败: %v", reqDto.VenueId, reqDto.FeatureName, err)
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "重试初始化失败: "+err.Error())
		return
	}

	util.Wlog(ctx).Infof("重试门店[%s]功能[%s]初始化成功", reqDto.VenueId, reqDto.FeatureName)
	Result_success[any](ctx, "重试成功")
}

// InitializeNewVenue 初始化新门店（内部接口）
// @Summary 初始化新门店
// @Description 为新创建的门店初始化所有必需功能（内部接口）
// @Tags 商务后台-门店初始化
// @Accept json
// @Produce json
// @Param body body req.InitializeNewVenueReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/venue/init-new [post]
func (c *VenueInitializationController) InitializeNewVenue(ctx *gin.Context) {
	var reqDto req.InitializeNewVenueReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误: "+err.Error())
		return
	}

	util.Wlog(ctx).Infof("开始初始化新门店[%s]", reqDto.VenueId)

	// 调用应用服务初始化新门店
	err := c.initService.InitializeNewVenue(ctx, reqDto.VenueId)
	if err != nil {
		util.Wlog(ctx).Errorf("初始化新门店[%s]失败: %v", reqDto.VenueId, err)
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "初始化新门店失败: "+err.Error())
		return
	}

	util.Wlog(ctx).Infof("初始化新门店[%s]成功", reqDto.VenueId)
	Result_success[any](ctx, "初始化成功")
}
