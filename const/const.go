package _const

import "time"

const (
	// 房间状态 room.status
	ROOM_STATUS_IDLE       = "idle"       // 空闲
	ROOM_STATUS_WITH_GUEST = "with_guest" // 带客
	ROOM_STATUS_IN_USE     = "in_use"     // 使用中
	ROOM_STATUS_AFTER_USE  = "after_use"  // 使用后
	ROOM_STATUS_CLEANING   = "cleaning"   // 清扫中
	ROOM_STATUS_FAULT      = "fault"      // 故障

	// 订单状态  session.status -> order.status
	ORDER_STATUS_UNPAID    = "unpaid"    // 未结
	ORDER_STATUS_PAID      = "paid"      // 已结
	ORDER_STATUS_PAYING    = "paying"    // 支付中
	ORDER_STATUS_WAIT_PAY  = "wait_pay"  // 待结
	ORDER_STATUS_BELOW_MIN = "below_min" // 不满低消
	ORDER_STATUS_REFUNDED  = "refunded"  // 已退款
	ORDER_STATUS_REFUNDING = "refunding" // 退款中

	// 标签 room.tag []string
	ROOM_TAG_LOCKED = "locked" // 锁房

	// 标记 room.tag 动态加上
	ROOM_MARK_UNION  = "union"  // 联台 此状态无效，其他方式实现
	ROOM_MARK_BOOKED = "booked" // 预定 此状态无效，其他方式实现

	// 订单标签 session(主订单) or order(子订单)标识
	ORDER_MARK_OPENING          = "opening"          // 开台 tag in session.info
	ORDER_MARK_OPENING_CONTINUE = "opening_continue" // 开台续台 tag in session.info
	ORDER_MARK_TRANSFER         = "transfer"         // 转台 tag in session.info 已开台的房间转到另一个空闲的房间
	ORDER_MARK_ATTACH           = "attach"           // 联房 空房加入到已开台的房间,并共享sessionid
	ORDER_MARK_MERGE            = "merge"            // 并房 tag in session.info 一个房间的订单合并到另有一个房间来支付
	ORDER_MARK_SWAP             = "swap"             // 互换 tag in session.info 两个已开台的房间互换
	ORDER_MARK_ADDITIONAL       = "additional"       // 点单 tag in order.mark
	ORDER_MARK_GIFT_PRODUCT     = "gift_product"     // 商品 tag in order.mark
	ORDER_MARK_GIFT_TIME        = "gift_time"        // 时间 tag in order.mark
	ORDER_MARK_CANCEL           = "cancel"           // 取消
	ORDER_MARK_REFUND           = "refund"           // 退款
	ORDER_MARK_OTHER            = "other"            // 其他

	ORDER_TYPE_PRODUCT  = "product"  // 订单类型-商品
	ORDER_TYPE_ROOMPLAN = "roomplan" // 订单类型-房间

	ORDER_PRODUCT_SRC_IN  = "in"  // 套餐内
	ORDER_PRODUCT_SRC_OUT = "out" // 套餐外

	// 支付状态
	PAY_STATUS_PAID        = "paid"        // 已支付
	PAY_STATUS_UNPAID      = "unpaid"      // 未支付
	PAY_STATUS_REFUND      = "refund"      // 已退款
	PAY_STATUS_REFUND_PART = "refund_part" // 部分退款
	PAY_STATUS_REFUND_FAIL = "refund_fail" // 退款失败
	PAY_STATUS_REFUNDING   = "refunding"   // 退款中
	PAY_STATUS_CLOSE       = "close"       // 订单关闭

	// 支付类型
	PAY_TYPE_RECORD_CASH     = "cash"          // 现金-记账
	PAY_TYPE_RECORD_WECHAT   = "wechat_record" // 微信-记账
	PAY_TYPE_RECORD_ALIPAY   = "alipay_record" // 支付宝-记账
	PAY_TYPE_RECORD_BANK     = "bank_record"   // 银联-记账
	PAY_TYPE_LESHUA_BSHOWQR  = "bShowQR"       // 乐刷付款码
	PAY_TYPE_MEMBER_CARD     = "member_card"   // 会员卡
	PAY_TYPE_WECHAT_DOUYIN   = "douyin"        // 抖音-记账
	PAY_TYPE_WECHAT_MEITUAN  = "meituan"       // 美团-记账
	PAY_TYPE_WECHAT_KOUBEI   = "koubei"        // 口碑-记账
	PAY_TYPE_WECHAT_POS      = "pos"           // POS机-记账
	PAY_TYPE_WECHAT_GUAZHANG = "guazhang"      // 挂账-记账
	PAY_TYPE_FREE_ORDER      = "free"          // 免费-记账
	// 扩展支付类型
	PAY_TYPE_LESHUA_SIMPLE  = "lshSimple"      // 乐刷简易支付
	PAY_TYPE_WECHAT_MINIAPP = "wechat_miniapp" // 微信小程序

	// 支付类型名称常量
	PAY_TYPE_NAME_CASH            = "现金"
	PAY_TYPE_NAME_WECHAT_RECORD   = "微信"
	PAY_TYPE_NAME_ALIPAY_RECORD   = "支付宝"
	PAY_TYPE_NAME_BANK_RECORD     = "银联"
	PAY_TYPE_NAME_LESHUA_BSHOWQR  = "乐刷付款码"
	PAY_TYPE_NAME_MEMBER_CARD     = "会员卡"
	PAY_TYPE_NAME_WECHAT_DOUYIN   = "抖音"
	PAY_TYPE_NAME_WECHAT_MEITUAN  = "美团"
	PAY_TYPE_NAME_WECHAT_KOUBEI   = "口碑"
	PAY_TYPE_NAME_WECHAT_POS      = "POS机"
	PAY_TYPE_NAME_WECHAT_GUAZHANG = "挂账"
	PAY_TYPE_NAME_FREE_ORDER      = "免费订单"
	// 扩展支付类型
	PAY_TYPE_NAME_LESHUA_SIMPLE  = "乐刷简易支付"
	PAY_TYPE_NAME_WECHAT_MINIAPP = "微信小程序"

	REFUND_WAY_TYPE_BACK = "back" // 原路径返回
	REFUND_WAY_TYPE_CASH = "cash" // 现金退款

	PAY_BILL_BUSINESS_TYPE_MEMBER  = "member"  // 会员
	PAY_BILL_BUSINESS_TYPE_DEFAULT = "default" // 默认

	MEMBER_ORDER_STATUS_UNPAID = "unpaid" // 未结
	MEMBER_ORDER_STATUS_PAID   = "paid"   // 已结
	MEMBER_ORDER_STATUS_PAYING = "paying" // 支付中

)

// 支付类型退款顺序,值越小，优先级越高
// 会员卡>乐刷>微信记账>支付宝记账>银联记账>现金
// 退款时，先退现金，再退乐刷，再退微信记账，再退支付宝记账，再退银联记账，最后退会员卡
// 会员卡内退款顺序，商品赠金/房费赠金>通用赠金>本金
var REFUND_ORDER_WIGHT = map[string]int{
	PAY_TYPE_MEMBER_CARD:     1,  // 会员卡
	PAY_TYPE_LESHUA_BSHOWQR:  2,  // 乐刷付款码
	PAY_TYPE_RECORD_WECHAT:   3,  // 微信记账
	PAY_TYPE_RECORD_ALIPAY:   4,  // 支付宝记账
	PAY_TYPE_RECORD_BANK:     5,  // 银联记账
	PAY_TYPE_RECORD_CASH:     6,  // 现金
	PAY_TYPE_WECHAT_DOUYIN:   7,  // 抖音
	PAY_TYPE_WECHAT_MEITUAN:  8,  // 美团
	PAY_TYPE_WECHAT_KOUBEI:   9,  // 口碑
	PAY_TYPE_WECHAT_POS:      10, // POS机
	PAY_TYPE_WECHAT_GUAZHANG: 11, // 挂账
	PAY_TYPE_FREE_ORDER:      12, // 免费
}

// 支付类型名称映射
var PAY_TYPE_NAME_MAP = map[string]string{
	PAY_TYPE_RECORD_CASH:     PAY_TYPE_NAME_CASH,
	PAY_TYPE_RECORD_WECHAT:   PAY_TYPE_NAME_WECHAT_RECORD,
	PAY_TYPE_RECORD_ALIPAY:   PAY_TYPE_NAME_ALIPAY_RECORD,
	PAY_TYPE_RECORD_BANK:     PAY_TYPE_NAME_BANK_RECORD,
	PAY_TYPE_LESHUA_BSHOWQR:  PAY_TYPE_NAME_LESHUA_BSHOWQR,
	PAY_TYPE_LESHUA_SIMPLE:   PAY_TYPE_NAME_LESHUA_SIMPLE,
	PAY_TYPE_MEMBER_CARD:     PAY_TYPE_NAME_MEMBER_CARD,
	PAY_TYPE_WECHAT_MINIAPP:  PAY_TYPE_NAME_WECHAT_MINIAPP,
	PAY_TYPE_WECHAT_DOUYIN:   PAY_TYPE_NAME_WECHAT_DOUYIN,
	PAY_TYPE_WECHAT_MEITUAN:  PAY_TYPE_NAME_WECHAT_MEITUAN,
	PAY_TYPE_WECHAT_KOUBEI:   PAY_TYPE_NAME_WECHAT_KOUBEI,
	PAY_TYPE_WECHAT_POS:      PAY_TYPE_NAME_WECHAT_POS,
	PAY_TYPE_WECHAT_GUAZHANG: PAY_TYPE_NAME_WECHAT_GUAZHANG,
	PAY_TYPE_FREE_ORDER:      PAY_TYPE_NAME_FREE_ORDER,
}

// PayTypeConfig 支付类型配置值对象
type PayTypeConfig struct {
	PayType       string `json:"type"`          // 支付类型
	Label         string `json:"label"`         // 显示标签
	Enabled       bool   `json:"enabled"`       // 是否启用
	IsPreset      bool   `json:"isPreset"`      // 是否预设
	Sort          int    `json:"sort"`          // 排序
	IsNetProfit   bool   `json:"isNetProfit"`   // 是否是净收益
	IsTotalProfit bool   `json:"isTotalProfit"` // 是否实收
}

// [{"type":"bShowQR","label":"扫码枪","enabled":true,"isPreset":true,"sort":0}]
var PAY_TYPE_ALL = []PayTypeConfig{
	{PayType: "bShowQR", Label: "扫码枪", Enabled: true, IsPreset: true, Sort: 0, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "pos", Label: "POS机", Enabled: true, IsPreset: true, Sort: 1, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "cash", Label: "现金", Enabled: true, IsPreset: true, Sort: 2, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "bank_record", Label: "银行卡", Enabled: true, IsPreset: true, Sort: 3, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "wechat_record", Label: "微信", Enabled: true, IsPreset: true, Sort: 4, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "alipay_record", Label: "支付宝", Enabled: true, IsPreset: true, Sort: 5, IsNetProfit: true, IsTotalProfit: true},
	{PayType: "member_card", Label: "会员卡", Enabled: true, IsPreset: true, Sort: 6, IsNetProfit: false, IsTotalProfit: false},
	{PayType: "meituan", Label: "美团", Enabled: true, IsPreset: true, Sort: 7, IsNetProfit: false, IsTotalProfit: false},
	{PayType: "douyin", Label: "抖音", Enabled: true, IsPreset: true, Sort: 8, IsNetProfit: false, IsTotalProfit: false},
	{PayType: "guazhang", Label: "挂帐", Enabled: false, IsPreset: true, Sort: 9, IsNetProfit: false, IsTotalProfit: false},
}

// 星期
const (
	WEEK_DAY_MONDAY    = 1
	WEEK_DAY_TUESDAY   = 2
	WEEK_DAY_WEDNESDAY = 3
	WEEK_DAY_THURSDAY  = 4
	WEEK_DAY_FRIDAY    = 5
	WEEK_DAY_SATURDAY  = 6
	WEEK_DAY_SUNDAY    = 7
)

var PAY_TYPE_SUPPORTS = []string{PAY_TYPE_RECORD_CASH, PAY_TYPE_RECORD_WECHAT, PAY_TYPE_RECORD_ALIPAY, PAY_TYPE_RECORD_BANK, PAY_TYPE_LESHUA_BSHOWQR, PAY_TYPE_MEMBER_CARD, PAY_TYPE_FREE_ORDER, PAY_TYPE_LESHUA_SIMPLE, PAY_TYPE_WECHAT_DOUYIN, PAY_TYPE_WECHAT_MEITUAN, PAY_TYPE_WECHAT_KOUBEI, PAY_TYPE_WECHAT_POS, PAY_TYPE_WECHAT_GUAZHANG, PAY_TYPE_WECHAT_MINIAPP}
var PAY_TYPE_SUPPORTS_RECHARGE = []string{PAY_TYPE_RECORD_CASH, PAY_TYPE_RECORD_WECHAT, PAY_TYPE_RECORD_ALIPAY, PAY_TYPE_RECORD_BANK, PAY_TYPE_LESHUA_BSHOWQR}
var PAY_TYPE_RECORDS = []string{PAY_TYPE_RECORD_CASH, PAY_TYPE_RECORD_WECHAT, PAY_TYPE_RECORD_ALIPAY, PAY_TYPE_RECORD_BANK, PAY_TYPE_WECHAT_DOUYIN, PAY_TYPE_WECHAT_MEITUAN, PAY_TYPE_WECHAT_KOUBEI, PAY_TYPE_WECHAT_POS, PAY_TYPE_WECHAT_GUAZHANG, PAY_TYPE_WECHAT_MINIAPP}
var REFUND_WAY_TYPES = []string{REFUND_WAY_TYPE_BACK, REFUND_WAY_TYPE_CASH}

const (
	PAGE_SIZE_DEFAULT   = 10 // 分页大小
	PAGE_NUMBER_DEFAULT = 1  // 分页页码
)

const (
	LOGIN_STATUS_SUCCESS = "success"
	LOGIN_STATUS_EXPIRED = "expired"
	LOGIN_STATUS_FAILED  = "failed"
)

// 登录客户端类型
const (
	LOGIN_CLIENT_TYPE_MINIAPP = "miniapp" // 小程序客户端
	LOGIN_CLIENT_TYPE_SHOUYIN = "shouyin" // 收银端客户端
	LOGIN_CLIENT_TYPE_MANAGER = "manager" // 掌柜系统客户端
)

const (
	V2_CONSUMPTION_MODE_TIME_CHARGE = "timeCharge" // 买钟
	V2_CONSUMPTION_MODE_BYOUT       = "buyout"     // 买断
	V2_CONSUMPTION_MODE_SALES       = "sales"      // 核销

	V2_ORDER_TYPE_PRODUCT  = "product"  // 订单类型-商品
	V2_ORDER_TYPE_ROOMPLAN = "roomplan" // 订单类型-房间

	V2_ORDER_MARK_OPENING          = "opening"          // 开台
	V2_ORDER_MARK_OPENING_CONTINUE = "opening_continue" // 开台续台
	V2_ORDER_MARK_ADDITIONAL       = "additional"       // 点单
	V2_ORDER_MARK_MINIAPP_PAY      = "miniapp_pay"      // 小程序点单
	V2_ORDER_MARK_GIFT_PRODUCT     = "gift_product"     // 商品
	V2_ORDER_MARK_GIFT_TIME        = "gift_time"        // 时间

	V2_ROOM_STATUS_IDLE     = "idle"       // 空闲
	V2_ROOM_STATUS_IN_USE   = "in_use"     // 使用中
	V2_ROOM_STATUS_FAULT    = "fault"      // 故障
	V2_ROOM_STATUS_CLEANING = "cleaning"   // 清扫中
	V2_ROOM_STATUS_GUEST    = "with_guest" // 带客

	V2_SESSION_STATUS_OPENING   = "opening"   // 开台
	V2_SESSION_STATUS_ENDING    = "ending"    // 关台
	V2_SESSION_STATUS_REOPENING = "reopening" // 重开

	V2_SESSION_PAY_STATUS_UNPAID = "unpaid" // 未支付
	V2_SESSION_PAY_STATUS_PAID   = "paid"   // 已支付

	V2_ORDER_DIRECTION_NORMAL = "normal" // 正常
	V2_ORDER_DIRECTION_REFUND = "refund" // 退款

	V2_ORDER_MARK_TYPE_NORMAL = "normal" // 正常
	V2_ORDER_MARK_TYPE_CANCEL = "cancel" // 取消  仅在转台的时才会产生取消状态的订单，对应收款信息保持不变

	V2_ORDER_STATUS_UNPAID = "unpaid" // 未结
	V2_ORDER_STATUS_PAID   = "paid"   // 已结

	V2_PAY_BILL_DIRECTION_NORMAL = "normal" // 正常
	V2_PAY_BILL_DIRECTION_REFUND = "refund" // 退款

	V2_PAY_BILL_STATUS_WAIT = "wait" // 待支付
	V2_PAY_BILL_STATUS_PAID = "paid" // 已支付

	V2_PAY_RECORD_STATUS_WAIT    = "wait"    // 待支付
	V2_PAY_RECORD_STATUS_SUCCESS = "success" // 已支付
	V2_PAY_RECORD_STATUS_FAILED  = "failed"  // 支付失败

	V2_ORDER_PRODUCT_SRC_IN  = "in"  // 套餐内
	V2_ORDER_PRODUCT_SRC_OUT = "out" // 套餐外

	// 按原房间套餐转台:bySrc, 按目标房间转台:byDst
	V2_TRANSFER_MODE_BY_SRC = "bySrc"
	V2_TRANSFER_MODE_BY_DST = "byDst"

	V2_PAY_CALLBACK_TYPE_COMMON = "common" // 通用
	V2_PAY_CALLBACK_TYPE_PAY    = "pay"    // 支付
	V2_PAY_CALLBACK_TYPE_REFUND = "refund" // 退款

	V2_ROOM_OPERATION_TYPE_OPENING = "opening" // 开台/续台
	V2_ROOM_OPERATION_TYPE_ATTACH  = "attach"  // 联房
	V2_ROOM_OPERATION_TYPE_MERGE   = "merge"   // 并房

	V2_MEMBER_RECHARGE_RECORD_DIRECTION_RECHARGE = "recharge" // 充值
	V2_MEMBER_RECHARGE_RECORD_DIRECTION_REFUND   = "refund"   // 退款

	V2_MEMBER_RECHARGE_BILL_DIRECTION_NORMAL = "normal" // 正常
	V2_MEMBER_RECHARGE_BILL_DIRECTION_REFUND = "refund" // 退款

	V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_CONSUME = "consume" // 消费
	V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_REFUND  = "refund"  // 退款

	V2_MEMBER_RECHARGE_BILL_STATUS_WAIT = "wait" // 待支付
	V2_MEMBER_RECHARGE_BILL_STATUS_PAID = "paid" // 已支付

	V2_MEMBER_RECHARGE_RECORD_STATUS_WAIT    = "wait"    // 待支付
	V2_MEMBER_RECHARGE_RECORD_STATUS_SUCCESS = "success" // 已支付
	V2_MEMBER_RECHARGE_RECORD_STATUS_FAILED  = "failed"  // 支付失败

	V2_MEMBER_CARD_LEVEL_COMMON  = "common"  // 普通卡
	V2_MEMBER_CARD_LEVEL_GOLD    = "gold"    // 黄金卡
	V2_MEMBER_CARD_LEVEL_DIAMOND = "diamond" // 钻石卡

	V2_MEMBER_CARD_TYPE_PHYSICAL   = "physical"   // 实体卡
	V2_MEMBER_CARD_TYPE_VIRTUAL    = "virtual"    // 虚拟卡
	V2_MEMBER_CARD_TYPE_ELECTRONIC = "electronic" // 电子卡

	// 会员卡状态
	V2_MEMBER_CARD_STATUS_NORMAL    = "normal"    // 正常状态：会员卡处于正常使用状态，可以进行充值、消费、积分兑换等所有操作
	V2_MEMBER_CARD_STATUS_LOST      = "lost"      // 挂失状态：会员卡被会员挂失，禁止消费和充值操作，需要记录挂失原因
	V2_MEMBER_CARD_STATUS_FROZEN    = "frozen"    // 冻结状态：由管理员对会员卡进行冻结，禁止所有操作，需要记录冻结原因
	V2_MEMBER_CARD_STATUS_EXPIRED   = "expired"   // 过期状态：会员卡超过有效期限，不能再进行正常使用
	V2_MEMBER_CARD_STATUS_CANCELLED = "cancelled" // 注销状态：会员卡被永久注销，不再可用

	// 次卡状态
	V2_MEMBER_CARD_TIMES_STATUS_INACTIVE     = "inactive"     // 未激活：次卡已开卡但尚未支付激活费用
	V2_MEMBER_CARD_TIMES_STATUS_UNUSED       = "unused"       // 未核销：次卡已激活但尚未使用
	V2_MEMBER_CARD_TIMES_STATUS_PARTIAL_USED = "partial_used" // 部分核销：次卡已使用部分次数，仍有剩余次数可用
	V2_MEMBER_CARD_TIMES_STATUS_USED         = "used"         // 已核销：次卡次数已全部用完
	V2_MEMBER_CARD_TIMES_STATUS_FROZEN       = "frozen"       // 冻结状态：次卡被管理员冻结，暂时无法使用
	V2_MEMBER_CARD_TIMES_STATUS_REFUNDED     = "refunded"     // 已退款：次卡已办理退款，不可再使用

	V2_MEMBER_GENDER_MALE   = "male"   // 男
	V2_MEMBER_GENDER_FEMALE = "female" // 女

	V2_LESHUA_PAY_CALLBACK_TYPE_MEMBER = "member" // 会员充值回调和收银台回调区分

	V2_MEMBER_CARD_OPERATION_TYPE_OPEN_CARD = "open_card" // 开卡
	V2_MEMBER_CARD_OPERATION_TYPE_CANCEL    = "cancel"    // 注销
	V2_MEMBER_CARD_OPERATION_TYPE_RENEW     = "renew"     // 续卡
	V2_MEMBER_CARD_OPERATION_TYPE_FROZEN    = "frozen"    // 冻结
	V2_MEMBER_CARD_OPERATION_TYPE_UNFROZEN  = "unfrozen"  // 解冻
	V2_MEMBER_CARD_OPERATION_TYPE_EXPIRE    = "expire"    // 过期
	V2_MEMBER_CARD_OPERATION_TYPE_LOST      = "lost"      // 挂失
	V2_MEMBER_CARD_OPERATION_TYPE_UNLOST    = "unlost"    // 解挂
	V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE  = "recharge"  // 充值
	V2_MEMBER_CARD_OPERATION_TYPE_REFUND    = "refund"    // 退款

	V2_CALL_MESSAGE_STATUS_UNPROCESSED = 0 // 未处理
	V2_CALL_MESSAGE_STATUS_PROCESSED   = 1 // 已处理
	V2_CALL_MESSAGE_STATUS_CANCELLED   = 2 // 已取消

	V2_CALL_MESSAGE_SRC_TYPE_DEFAULT = "default" // 默认
	V2_CALL_MESSAGE_SRC_TYPE_MINIAPP = "miniapp" // 小程序
	V2_CALL_MESSAGE_SRC_TYPE_SHOUYIN = "shouyin" // 收银端

	V2_ORDER_REFUND_TAG_PAID_REFUND   = "paid_refund"   // 已付退款
	V2_ORDER_REFUND_TAG_UNPAID_REFUND = "unpaid_refund" // 未付退款

	// 库存变动记录相关常量
	V2_PRODUCT_STOCK_CHANGE_RECORD_ACTION_TYPE_ADD   = "add"   // 增加库存
	V2_PRODUCT_STOCK_CHANGE_RECORD_ACTION_TYPE_MINUS = "minus" // 减少库存

	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_ORDER           = "order"           // 订单
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_REFUND          = "refund"          // 退款
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_PRODUCT_STORAGE = "product_storage" // 存酒
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_IN    = "inventory_in"    // 入库
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_OUT   = "inventory_out"   // 出库
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_CHECK = "inventory_check" // 盘点
)

var (
	SUPPORT_MEMBER_CARD_LEVELS = []string{V2_MEMBER_CARD_LEVEL_COMMON, V2_MEMBER_CARD_LEVEL_GOLD, V2_MEMBER_CARD_LEVEL_DIAMOND}
	SUPPORT_MEMBER_CARD_TYPES  = []string{V2_MEMBER_CARD_TYPE_PHYSICAL, V2_MEMBER_CARD_TYPE_VIRTUAL, V2_MEMBER_CARD_TYPE_ELECTRONIC}
	SUPPORT_MEMBER_GENDERS     = []string{V2_MEMBER_GENDER_MALE, V2_MEMBER_GENDER_FEMALE}

	// 库存变动记录来源名称映射
	V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_NAME_MAP = map[string]string{
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_ORDER:           "订单",
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_REFUND:          "退款",
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_PRODUCT_STORAGE: "存取酒",
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_IN:    "入库",
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_OUT:   "出库",
		V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_CHECK: "盘点",
	}
)

const (

	// 包厢价格会员优惠方式
	ROOM_MEMBER_DISCOUNT_TYPE_NONE     = 0 // 无
	ROOM_MEMBER_DISCOUNT_TYPE_PRICE    = 1 // 会员价
	ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT = 2 // 会员折扣

	// 商品价格会员优惠方式
	PRODUCT_MEMBER_DISCOUNT_TYPE_NONE     = 0 // 无
	PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE    = 1 // 会员价
	PRODUCT_MEMBER_DISCOUNT_TYPE_DISCOUNT = 2 // 会员折扣

	DISCOUNT_TYPE_ALL  = 0 // 全部打折
	DISCOUNT_TYPE_PART = 1 // 仅超过低消部分打折
)

// 用户性别常量
const (
	USER_GENDER_MALE   = "male"   // 男
	USER_GENDER_FEMALE = "female" // 女
)

// 获取所有支持的用户性别类型
func GetSupportUserGenders() []string {
	return []string{USER_GENDER_MALE, USER_GENDER_FEMALE}
}

const (
	VenueAuditStatusPass = 1 // 审核通过
)

const (
	Venue_AUTH_CODE_STATUS_UNUSED  = 0 // 未使用
	Venue_AUTH_CODE_STATUS_IN_USE  = 1 // 使用中
	Venue_AUTH_CODE_STATUS_EXPIRED = 2 // 已过期
)

// 库存记录类型常量
const (
	INVENTORY_RECORD_TYPE_INBOUND  = "INBOUND"  // 入库（包含正常入库和冲销记录，通过is_reversed字段和数量正负区分）
	INVENTORY_RECORD_TYPE_OUTBOUND = "OUTBOUND" // 出库
	INVENTORY_RECORD_TYPE_ADJUST   = "ADJUST"   // 调整
)

// 库存记录状态常量
const (
	INVENTORY_RECORD_STATUS_ACTIVE    = ""          // 正常激活状态（默认空字符串）
	INVENTORY_RECORD_STATUS_CANCELLED = "CANCELLED" // 已取消状态
)

const (
	SMS_VALIDATE_CODE_KEY = "sms_validate_code:%s:%s:%s" // 短信验证码缓存key: clientType:phone:validateType

	SMS_VALIDATE_CODE_TYPE_MEMBER_PAY    = "member_pay"    // 会员支付验证码
	SMS_VALIDATE_CODE_TYPE_LOGIN         = "login"         // 登录验证码
	SMS_VALIDATE_CODE_TYPE_WINE_WITHDRAW = "wine_withdraw" // 取酒验证码

	SMS_VALIDATE_CODE_EXPIRE_MINUTES = 5 // 短信验证码有效期（分钟）
)

// SMS验证码有效期时间
var SMS_VALIDATE_CODE_EXPIRE_DURATION = time.Duration(SMS_VALIDATE_CODE_EXPIRE_MINUTES) * time.Minute

// 有效的SMS验证码类型列表
var ValidSmsValidateCodeTypes = map[string]bool{
	SMS_VALIDATE_CODE_TYPE_MEMBER_PAY:    true,
	SMS_VALIDATE_CODE_TYPE_LOGIN:         true,
	SMS_VALIDATE_CODE_TYPE_WINE_WITHDRAW: true,
}

// IsValidSmsValidateCodeType 验证SMS验证码类型是否有效
func IsValidSmsValidateCodeType(validateType string) bool {
	return ValidSmsValidateCodeTypes[validateType]
}

// 打印记录状态常量
const (
	PRINT_STATUS_PENDING = 0 // 待打印/未打印
	PRINT_STATUS_SUCCESS = 1 // 打印成功
	PRINT_STATUS_FAILED  = 2 // 打印失败
)

// 门店功能初始化状态常量
const (
	INIT_STATUS_PENDING     = 0 // 待初始化
	INIT_STATUS_IN_PROGRESS = 1 // 初始化中
	INIT_STATUS_SUCCESS     = 2 // 成功
	INIT_STATUS_FAILED      = 3 // 失败
)

// 系统功能初始化状态常量
const (
	SYSTEM_INIT_STATUS_PENDING     = 0 // 待初始化
	SYSTEM_INIT_STATUS_IN_PROGRESS = 1 // 初始化中
	SYSTEM_INIT_STATUS_COMPLETED   = 2 // 完成
)
