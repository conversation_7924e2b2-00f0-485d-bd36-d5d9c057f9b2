# 门店数据初始化框架与新功能上线架构设计文档

## 1. 概述

### 1.1 背景
随着ERP系统功能的不断扩展，新功能上线时需要为已有门店初始化相应的默认数据（如打印模板、价格方案、库存配置等）。传统的手动初始化方式存在效率低、易出错、难监控等问题。为了解决这些问题，我们设计了一套标准化的门店数据初始化框架。

### 1.2 设计目标
- **标准化**：所有新功能都遵循统一的初始化模式
- **可追溯**：每个门店的每个功能初始化都有完整记录
- **可重试**：支持失败重试和断点续传
- **依赖管理**：支持功能间的依赖关系处理
- **监控友好**：提供完整的初始化状态监控
- **版本管理**：支持功能升级时的数据迁移
- **异步处理**：大批量初始化不阻塞主流程

### 1.3 适用场景
- 新功能上线时的门店数据初始化
- 新门店创建时的基础数据准备
- 系统升级时的数据迁移

## 2. 架构设计

### 2.1 核心组件

#### 2.1.1 VenueDataInitializer（初始化器接口）
所有功能初始化器的统一接口，定义了初始化的标准流程。

```go
type VenueDataInitializer interface {
    GetFeatureName() string                                    // 获取功能名称
    GetVersion() string                                        // 获取初始化版本号
    IsRequired() bool                                          // 是否必需初始化
    CheckInitialized(ctx *gin.Context, venueId string) (bool, error) // 检查是否已初始化
    InitializeForVenue(ctx *gin.Context, venueId string) error       // 执行初始化
    GetDependencies() []string                                 // 获取依赖关系
}
```

#### 2.1.2 VenueInitializationManager（初始化管理器）
负责管理所有初始化器，协调初始化流程，处理依赖关系。

```go
type VenueInitializationManager struct {
    initializers    map[string]VenueDataInitializer  // 已注册的初始化器
    db             *gorm.DB                          // 数据库连接
    featureVersions map[string]string                // 功能版本管理
    taskManager    *InitializationTaskManager       // 任务管理器
}
```

#### 2.1.3 InitializationTaskManager（任务管理器）
管理初始化任务的生命周期，提供进度跟踪和状态监控。

```go
type InitializationTask struct {
    ID           string     // 任务ID
    FeatureName  string     // 功能名称
    Status       string     // 任务状态
    TotalCount   int        // 总门店数
    SuccessCount int        // 成功数量
    FailedCount  int        // 失败数量
    StartTime    time.Time  // 开始时间
    EndTime      *time.Time // 结束时间
    ErrorMessage string     // 错误信息
}
```

> **注意**：`InitializationTask` 是内存中的数据结构，用于临时管理任务状态和进度监控，**不需要创建对应的数据库表**。持久化存储通过 `venue_feature_initialization` 和 `system_feature_versions` 两个表来实现。如果需要支持应用重启后恢复任务状态，可以考虑使用Redis等缓存存储或创建专门的任务表。

#### 2.1.4 FeatureDeploymentService（功能部署服务）
处理新功能上线时的自动检测和批量初始化。

### 2.2 数据模型

#### 2.2.1 venue_feature_initialization（门店功能初始化记录表）
```sql
CREATE TABLE `venue_feature_initialization` (
    `id` varchar(64) NOT NULL COMMENT 'ID',
    `venue_id` varchar(64) NOT NULL COMMENT '门店ID',
    `feature_name` varchar(100) NOT NULL COMMENT '功能名称',
    `version` varchar(20) NOT NULL COMMENT '初始化版本',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0=待初始化，1=初始化中，2=成功，3=失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `init_time` bigint NOT NULL DEFAULT 0 COMMENT '初始化时间',
    `ctime` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime` bigint NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_venue_feature` (`venue_id`, `feature_name`),
    KEY `idx_venue_id` (`venue_id`),
    KEY `idx_feature_name` (`feature_name`),
    KEY `idx_status` (`status`)
) COMMENT='门店功能初始化记录表';
```

#### 2.2.2 system_feature_versions（系统功能版本管理表）
```sql
CREATE TABLE `system_feature_versions` (
    `id` varchar(64) NOT NULL COMMENT 'ID',
    `feature_name` varchar(100) NOT NULL COMMENT '功能名称',
    `version` varchar(20) NOT NULL COMMENT '版本号',
    `deploy_time` bigint NOT NULL COMMENT '部署时间',
    `init_status` tinyint NOT NULL DEFAULT 0 COMMENT '初始化状态：0=待初始化，1=初始化中，2=完成',
    `total_venues` int NOT NULL DEFAULT 0 COMMENT '总门店数',
    `initialized_venues` int NOT NULL DEFAULT 0 COMMENT '已初始化门店数',
    `failed_venues` int NOT NULL DEFAULT 0 COMMENT '失败门店数',
    `ctime` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime` bigint NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_feature_version` (`feature_name`, `version`),
    KEY `idx_feature_name` (`feature_name`),
    KEY `idx_deploy_time` (`deploy_time`)
) COMMENT='系统功能版本管理表';
```

### 2.3 两个核心表的区别和用途

#### 2.3.1 表的职责划分

**`venue_feature_initialization`（门店功能初始化记录表）**
- **粒度**：门店级别，每个门店的每个功能一条记录
- **用途**：记录具体门店的具体功能初始化状态
- **数据量**：门店数 × 功能数（如1000个门店 × 10个功能 = 10000条记录）
- **主要查询**：查询某个门店的初始化状态、查询某个功能在所有门店的初始化情况

**`system_feature_versions`（系统功能版本管理表）**
- **粒度**：系统级别，每个功能版本一条记录
- **用途**：记录功能版本的整体部署和初始化进度
- **数据量**：功能数 × 版本数（如10个功能 × 平均3个版本 = 30条记录）
- **主要查询**：检测新功能、监控整体初始化进度、版本管理

#### 2.3.2 具体使用场景

**`venue_feature_initialization` 使用场景：**
```go
// 1. 检查门店A的打印模板功能是否已初始化
SELECT * FROM venue_feature_initialization
WHERE venue_id = 'venue_A' AND feature_name = 'print_template';

// 2. 查询打印模板功能在所有门店的初始化状态
SELECT venue_id, status FROM venue_feature_initialization
WHERE feature_name = 'print_template';

// 3. 查询门店A所有功能的初始化状态
SELECT feature_name, status FROM venue_feature_initialization
WHERE venue_id = 'venue_A';
```

**`system_feature_versions` 使用场景：**
```go
// 1. 应用启动时检测新功能（版本不存在的功能）
SELECT feature_name FROM registered_features
WHERE feature_name NOT IN (
    SELECT feature_name FROM system_feature_versions
    WHERE version = '1.0.0'
);

// 2. 监控打印模板功能的整体初始化进度
SELECT total_venues, initialized_venues, failed_venues
FROM system_feature_versions
WHERE feature_name = 'print_template' AND version = '1.0.0';

// 3. 查询所有功能的部署历史
SELECT feature_name, version, deploy_time, init_status
FROM system_feature_versions
ORDER BY deploy_time DESC;
```

#### 2.3.3 数据流转关系

1. **新功能部署时**：
   - 在 `system_feature_versions` 中创建功能版本记录
   - 开始批量初始化时，为每个门店在 `venue_feature_initialization` 中创建记录

2. **初始化过程中**：
   - 更新 `venue_feature_initialization` 中的具体门店状态
   - 同时更新 `system_feature_versions` 中的统计数据（成功数、失败数）

3. **查询监控时**：
   - 查看整体进度：查询 `system_feature_versions`
   - 查看具体门店状态：查询 `venue_feature_initialization`

## 3. 关键示例代码

### 3.1 打印模板初始化器示例

```go
// PrintTemplateInitializer 打印模板初始化器
type PrintTemplateInitializer struct {
    printTemplateRepo repository.PrintTemplateRepository
}

func (p *PrintTemplateInitializer) GetFeatureName() string {
    return "print_template"
}

func (p *PrintTemplateInitializer) GetVersion() string {
    return "1.0.0"
}

func (p *PrintTemplateInitializer) IsRequired() bool {
    return true // 打印功能是必需的
}

func (p *PrintTemplateInitializer) CheckInitialized(ctx *gin.Context, venueId string) (bool, error) {
    // 检查是否已有打印模板
    templates, err := p.printTemplateRepo.FindByVenueID(ctx, venueId)
    if err != nil {
        return false, err
    }

    // 检查是否包含所有必需的模板类型
    requiredTypes := []string{"OPEN_TABLE", "SHIFT_CHANGE", "BILL", "REFUND"}
    existingTypes := make(map[string]bool)
    for _, template := range templates {
        existingTypes[template.GetTemplateType()] = true
    }

    for _, requiredType := range requiredTypes {
        if !existingTypes[requiredType] {
            return false, nil
        }
    }

    return true, nil
}

func (p *PrintTemplateInitializer) InitializeForVenue(ctx *gin.Context, venueId string) error {
    // 为门店创建默认打印模板
    templateTypes := []string{"OPEN_TABLE", "SHIFT_CHANGE", "BILL", "REFUND"}

    for _, templateType := range templateTypes {
        template, err := common.NewPrintTemplateEntity(
            venueId,
            templateType,
            fmt.Sprintf("%s默认模板", templateType),
            1,
            "默认",
            "80mm纸打印",
        )
        if err != nil {
            return fmt.Errorf("创建模板失败: %w", err)
        }

        if err := p.printTemplateRepo.Save(ctx, template); err != nil {
            return fmt.Errorf("保存模板失败: %w", err)
        }
    }

    return nil
}

func (p *PrintTemplateInitializer) GetDependencies() []string {
    return []string{} // 无依赖
}
```

### 3.2 初始化管理器核心方法

```go
// RegisterInitializer 注册初始化器
func (m *VenueInitializationManager) RegisterInitializer(initializer VenueDataInitializer) {
    m.initializers[initializer.GetFeatureName()] = initializer
}

// InitializeVenue 初始化门店
func (m *VenueInitializationManager) InitializeVenue(ctx *gin.Context, venueId string) error {
    // 按依赖顺序排序初始化器
    sortedInitializers := m.sortByDependencies()

    for _, initializer := range sortedInitializers {
        if !initializer.IsRequired() {
            continue // 跳过非必需功能
        }

        // 检查是否已初始化
        initialized, err := initializer.CheckInitialized(ctx, venueId)
        if err != nil {
            return fmt.Errorf("检查初始化状态失败: %w", err)
        }

        if initialized {
            continue // 已初始化，跳过
        }

        // 执行初始化
        if err := initializer.InitializeForVenue(ctx, venueId); err != nil {
            m.recordInitResult(ctx, venueId, initializer.GetFeatureName(), false, err.Error())
            return fmt.Errorf("初始化功能[%s]失败: %w", initializer.GetFeatureName(), err)
        }

        // 记录成功结果
        m.recordInitResult(ctx, venueId, initializer.GetFeatureName(), true, "")
    }

    return nil
}
```

### 3.3 批量初始化服务

```go
// InitializeExistingVenues 批量初始化已有门店
func (s *VenueInitializationService) InitializeExistingVenues(ctx *gin.Context, featureName string) error {
    // 1. 创建初始化任务
    taskId, err := s.taskManager.CreateTask(ctx, featureName)
    if err != nil {
        return fmt.Errorf("创建任务失败: %w", err)
    }

    // 2. 查询所有门店
    venues, err := s.getAllActiveVenues(ctx)
    if err != nil {
        return fmt.Errorf("获取门店列表失败: %w", err)
    }

    // 3. 异步批量处理
    go func() {
        s.processBatchInitialization(ctx, taskId, featureName, venues)
    }()

    return nil
}

func (s *VenueInitializationService) processBatchInitialization(ctx context.Context, taskId, featureName string, venues []*po.Venue) {
    batchSize := 50
    successCount := 0
    failedCount := 0

    for i := 0; i < len(venues); i += batchSize {
        end := i + batchSize
        if end > len(venues) {
            end = len(venues)
        }

        batch := venues[i:end]
        for _, venue := range batch {
            if err := s.manager.InitializeFeatureForVenue(ctx, venue.GetId(), featureName); err != nil {
                failedCount++
                util.Elog(ctx).Errorf("门店[%s]功能[%s]初始化失败: %v", venue.GetId(), featureName, err)
            } else {
                successCount++
            }
        }

        // 更新任务进度
        s.taskManager.UpdateProgress(ctx, taskId, successCount, failedCount)

        // 批次间暂停
        time.Sleep(time.Second * 2)
    }

    // 标记任务完成
    s.taskManager.CompleteTask(ctx, taskId)
}
```

## 4. 执行流程

### 4.1 新门店创建流程
1. 门店基础信息创建完成
2. 触发门店初始化服务
3. 按依赖顺序执行所有必需功能的初始化
4. 记录初始化结果
5. 门店可正常使用

### 4.2 新功能上线流程
1. **开发阶段**：实现功能初始化器并注册
2. **部署阶段**：应用启动时自动检测新功能
3. **初始化阶段**：创建批量初始化任务
4. **执行阶段**：分批异步处理所有门店
5. **监控阶段**：实时跟踪初始化进度
6. **完成阶段**：验证初始化结果

## 5. 关键特性

### 5.1 依赖管理
- 支持功能间的依赖关系定义
- 自动按依赖顺序执行初始化
- 依赖失败时阻止后续初始化

### 5.2 错误处理与重试
- 支持初始化失败的自动重试
- 可配置重试次数和间隔
- 详细的错误信息记录

### 5.3 性能优化
- 分批处理避免数据库压力
- 异步执行不阻塞主流程
- 可配置并发度控制

### 5.4 监控与告警
- 实时进度监控
- 失败率告警
- 性能指标统计

## 6. 接口设计

### 6.1 管理接口
- `POST /api/admin/venue/batch-init-feature` - 批量初始化功能
- `GET /api/admin/venue/init-status` - 查询初始化状态
- `GET /api/admin/system/init-progress` - 查询初始化进度
- `POST /api/admin/system/retry-failed-init` - 重试失败的初始化

### 6.2 内部服务接口
- `InitializeNewVenue(venueId)` - 初始化新门店
- `InitializeExistingVenues(featureName)` - 批量初始化已有门店

## 7. 架构图

### 7.1 整体架构图

```dot
digraph VenueInitializationArchitecture {
    rankdir=TB;
    node [shape=box, style=rounded];

    // 外部触发
    subgraph cluster_triggers {
        label="触发源";
        style=filled;
        color=lightgrey;

        NewVenue [label="新门店创建"];
        NewFeature [label="新功能上线"];
        ManualTrigger [label="手动触发"];
    }

    // 核心服务层
    subgraph cluster_services {
        label="服务层";
        style=filled;
        color=lightblue;

        VenueInitService [label="门店初始化服务\nVenueInitializationService"];
        DeploymentService [label="功能部署服务\nFeatureDeploymentService"];
    }

    // 核心管理层
    subgraph cluster_managers {
        label="管理层";
        style=filled;
        color=lightgreen;

        InitManager [label="初始化管理器\nVenueInitializationManager"];
        TaskManager [label="任务管理器\nInitializationTaskManager"];
    }

    // 初始化器层
    subgraph cluster_initializers {
        label="初始化器层";
        style=filled;
        color=lightyellow;

        PrintTemplateInit [label="打印模板初始化器\nPrintTemplateInitializer"];
        PricePlanInit [label="价格方案初始化器\nPricePlanInitializer"];
        InventoryInit [label="库存初始化器\nInventoryInitializer"];
        OtherInit [label="其他功能初始化器\n..."];
    }

    // 数据层
    subgraph cluster_data {
        label="数据层";
        style=filled;
        color=lightcoral;

        InitRecord [label="初始化记录表\nvenue_feature_initialization"];
        FeatureVersion [label="功能版本表\nsystem_feature_versions"];
        BusinessData [label="业务数据表\n(print_template, etc.)"];
    }

    // 监控层
    subgraph cluster_monitoring {
        label="监控层";
        style=filled;
        color=lightpink;

        ProgressMonitor [label="进度监控\nInitializationMonitor"];
        AlertSystem [label="告警系统"];
        Dashboard [label="监控面板"];
    }

    // 连接关系
    NewVenue -> VenueInitService;
    NewFeature -> DeploymentService;
    ManualTrigger -> VenueInitService;

    VenueInitService -> InitManager;
    DeploymentService -> InitManager;

    InitManager -> TaskManager;
    InitManager -> PrintTemplateInit;
    InitManager -> PricePlanInit;
    InitManager -> InventoryInit;
    InitManager -> OtherInit;

    TaskManager -> InitRecord;
    TaskManager -> FeatureVersion;

    PrintTemplateInit -> BusinessData;
    PricePlanInit -> BusinessData;
    InventoryInit -> BusinessData;
    OtherInit -> BusinessData;

    TaskManager -> ProgressMonitor;
    ProgressMonitor -> AlertSystem;
    ProgressMonitor -> Dashboard;
}
```

### 7.2 新功能上线流程图

```dot
digraph NewFeatureDeploymentFlow {
    rankdir=TB;
    node [shape=box, style=rounded];

    Start [label="应用启动", shape=ellipse, style=filled, color=lightgreen];

    DetectFeatures [label="检测新功能\nDetectNewFeatures()"];
    HasNewFeatures [label="是否有新功能?", shape=diamond];

    CreateTask [label="创建初始化任务\nCreateInitializationTask()"];
    GetVenues [label="获取所有门店\nGetAllActiveVenues()"];

    BatchProcess [label="分批处理门店\nProcessBatch()"];
    InitVenue [label="初始化单个门店\nInitializeForVenue()"];

    CheckDependency [label="检查依赖关系\nCheckDependencies()"];
    HasDependency [label="是否有依赖?", shape=diamond];
    InitDependency [label="先初始化依赖\nInitializeDependencies()"];

    ExecuteInit [label="执行初始化逻辑\nExecuteInitialization()"];
    RecordResult [label="记录初始化结果\nRecordInitResult()"];

    CheckSuccess [label="是否成功?", shape=diamond];
    UpdateProgress [label="更新进度\nUpdateTaskProgress()"];

    MoreVenues [label="还有更多门店?", shape=diamond];
    TaskComplete [label="任务完成\nMarkTaskComplete()"];

    SendAlert [label="发送告警\nSendAlert()"];
    End [label="结束", shape=ellipse, style=filled, color=lightcoral];

    // 流程连接
    Start -> DetectFeatures;
    DetectFeatures -> HasNewFeatures;
    HasNewFeatures -> CreateTask [label="是"];
    HasNewFeatures -> End [label="否"];

    CreateTask -> GetVenues;
    GetVenues -> BatchProcess;
    BatchProcess -> InitVenue;

    InitVenue -> CheckDependency;
    CheckDependency -> HasDependency;
    HasDependency -> InitDependency [label="是"];
    HasDependency -> ExecuteInit [label="否"];
    InitDependency -> ExecuteInit;

    ExecuteInit -> RecordResult;
    RecordResult -> CheckSuccess;
    CheckSuccess -> UpdateProgress [label="是"];
    CheckSuccess -> SendAlert [label="否"];
    SendAlert -> UpdateProgress;

    UpdateProgress -> MoreVenues;
    MoreVenues -> BatchProcess [label="是"];
    MoreVenues -> TaskComplete [label="否"];
    TaskComplete -> End;
}
```

### 7.3 门店初始化状态图

```dot
digraph VenueInitializationState {
    rankdir=LR;
    node [shape=box, style=rounded];

    // 状态节点
    Pending [label="待初始化\nPENDING", style=filled, color=lightgrey];
    InProgress [label="初始化中\nIN_PROGRESS", style=filled, color=lightyellow];
    Success [label="初始化成功\nSUCCESS", style=filled, color=lightgreen];
    Failed [label="初始化失败\nFAILED", style=filled, color=lightcoral];
    Retrying [label="重试中\nRETRYING", style=filled, color=orange];

    // 状态转换
    Pending -> InProgress [label="开始初始化"];
    InProgress -> Success [label="初始化成功"];
    InProgress -> Failed [label="初始化失败"];
    Failed -> Retrying [label="触发重试"];
    Retrying -> Success [label="重试成功"];
    Retrying -> Failed [label="重试失败"];

    // 自循环
    Failed -> Failed [label="达到最大重试次数"];
    Success -> Success [label="保持成功状态"];
}
```

## 8. 部署与运维

### 8.1 部署要求
- 数据库表结构更新
- 应用配置更新
- 监控指标配置

### 8.2 运维监控
- 初始化成功率监控
- 初始化耗时监控
- 失败门店告警

### 8.3 故障处理
- 初始化失败的排查流程
- 数据回滚机制
- 紧急修复流程

## 9. 实施计划

### 9.1 第一阶段：框架建设
- 实现核心接口和管理器
- 创建数据表结构
- 实现打印模板初始化器作为示例

### 9.2 第二阶段：功能完善
- 实现任务管理和进度监控
- 添加错误处理和重试机制
- 完善监控和告警功能

### 9.3 第三阶段：全面应用
- 将现有功能接入框架
- 所有新功能使用统一框架
- 性能优化和稳定性提升

### 9.4 第四阶段：持续优化
- 根据使用情况优化性能
- 增强监控和运维能力
- 扩展更多高级特性

## 10. 总结

本架构设计提供了一套完整的门店数据初始化解决方案，具有标准化、可监控、可扩展的特点。通过统一的接口和流程，确保了新功能上线时的数据初始化质量和效率，同时提供了完善的监控和运维能力。该架构不仅解决了当前的问题，也为未来的功能扩展奠定了坚实的基础。
